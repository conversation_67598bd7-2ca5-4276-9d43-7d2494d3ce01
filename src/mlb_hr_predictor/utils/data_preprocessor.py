"""
Data preprocessing utilities for MLB Home Run Predictor
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Optional, Any
from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder
from sklearn.impute import SimpleImputer
import warnings

warnings.filterwarnings("ignore")


class DataPreprocessor:
    """Handles data cleaning, preprocessing, and feature preparation"""

    def __init__(self):
        self.scalers = {}
        self.imputers = {}
        self.encoders = {}
        self.feature_stats = {}

    def clean_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """Clean raw data by handling missing values, outliers, and data types"""
        df_clean = df.copy()

        # Remove completely empty rows
        df_clean = df_clean.dropna(how="all")

        # Handle infinite values
        df_clean = df_clean.replace([np.inf, -np.inf], np.nan)

        # Convert data types
        df_clean = self._convert_data_types(df_clean)

        # Handle outliers
        df_clean = self._handle_outliers(df_clean)

        return df_clean

    def _convert_data_types(self, df: pd.DataFrame) -> pd.DataFrame:
        """Convert columns to appropriate data types"""
        df_converted = df.copy()

        # Identify numeric columns that should be numeric
        numeric_columns = [
            "career_hr_rate",
            "recent_hr_rate",
            "slugging_pct",
            "iso_power",
            "avg_exit_velocity",
            "max_exit_velocity",
            "hard_hit_rate",
            "pitcher_hr_per_9",
            "pitcher_fb_rate",
            "pitcher_velocity",
            "temperature",
            "wind_speed",
            "humidity",
            "ballpark_hr_factor",
        ]

        for col in numeric_columns:
            if col in df_converted.columns:
                df_converted[col] = pd.to_numeric(
                    df_converted[col], errors="coerce")

        # Identify categorical columns
        categorical_columns = [
            "career_stage",
            "platoon_advantage",
            "ballpark_name",
            "weather_conditions",
            "wind_direction",
        ]

        for col in categorical_columns:
            if col in df_converted.columns:
                df_converted[col] = df_converted[col].astype("category")

        return df_converted

    def _handle_outliers(self, df: pd.DataFrame, method: str = "iqr") -> pd.DataFrame:
        """Handle outliers using IQR method or z-score"""
        df_no_outliers = df.copy()

        numeric_columns = df_no_outliers.select_dtypes(
            include=[np.number]).columns

        for col in numeric_columns:
            if method == "iqr":
                Q1 = df_no_outliers[col].quantile(0.25)
                Q3 = df_no_outliers[col].quantile(0.75)
                IQR = Q3 - Q1
                lower_bound = Q1 - 1.5 * IQR
                upper_bound = Q3 + 1.5 * IQR

                # Cap outliers instead of removing them
                df_no_outliers[col] = df_no_outliers[col].clip(
                    lower_bound, upper_bound)

            elif method == "zscore":
                z_scores = np.abs(
                    (df_no_outliers[col] - df_no_outliers[col].mean())
                    / df_no_outliers[col].std()
                )
                df_no_outliers[col] = df_no_outliers[col].where(
                    z_scores < 3, df_no_outliers[col].median()
                )

        return df_no_outliers

    def handle_missing_values(
        self, df: pd.DataFrame, strategy: str = "smart"
    ) -> pd.DataFrame:
        """Handle missing values with different strategies"""
        df_imputed = df.copy()

        if strategy == "smart":
            # Use domain knowledge for imputation
            df_imputed = self._smart_imputation(df_imputed)
        else:
            # Use standard imputation
            numeric_columns = df_imputed.select_dtypes(
                include=[np.number]).columns
            categorical_columns = df_imputed.select_dtypes(
                include=["category", "object"]
            ).columns

            # Impute numeric columns
            if len(numeric_columns) > 0:
                if "numeric_imputer" not in self.imputers:
                    self.imputers["numeric_imputer"] = SimpleImputer(
                        strategy="median")
                    df_imputed[numeric_columns] = self.imputers[
                        "numeric_imputer"
                    ].fit_transform(df_imputed[numeric_columns])
                else:
                    df_imputed[numeric_columns] = self.imputers[
                        "numeric_imputer"
                    ].transform(df_imputed[numeric_columns])

            # Impute categorical columns
            if len(categorical_columns) > 0:
                if "categorical_imputer" not in self.imputers:
                    self.imputers["categorical_imputer"] = SimpleImputer(
                        strategy="most_frequent"
                    )
                    df_imputed[categorical_columns] = self.imputers[
                        "categorical_imputer"
                    ].fit_transform(df_imputed[categorical_columns])
                else:
                    df_imputed[categorical_columns] = self.imputers[
                        "categorical_imputer"
                    ].transform(df_imputed[categorical_columns])

        return df_imputed

    def _smart_imputation(self, df: pd.DataFrame) -> pd.DataFrame:
        """Smart imputation using domain knowledge"""
        df_smart = df.copy()

        # Baseball-specific imputation rules
        imputation_rules = {
            "career_hr_rate": 0.025,  # League average HR rate
            "recent_hr_rate": 0.025,
            "slugging_pct": 0.400,
            "iso_power": 0.150,
            "avg_exit_velocity": 87.0,
            "hard_hit_rate": 0.35,
            "pitcher_hr_per_9": 1.2,
            "pitcher_fb_rate": 0.35,
            "pitcher_velocity": 92.0,
            "temperature": 75,
            "wind_speed": 5,
            "humidity": 60,
            "ballpark_hr_factor": 1.0,
            "age": 28,
            "rest_days": 1,
            "games_played": 100,
        }

        for column, default_value in imputation_rules.items():
            if column in df_smart.columns:
                df_smart[column] = df_smart[column].fillna(default_value)

        # Fill remaining numeric columns with median
        numeric_columns = df_smart.select_dtypes(include=[np.number]).columns
        for col in numeric_columns:
            if df_smart[col].isnull().any():
                df_smart[col] = df_smart[col].fillna(df_smart[col].median())

        # Fill categorical columns with mode
        categorical_columns = df_smart.select_dtypes(
            include=["category", "object"]
        ).columns
        for col in categorical_columns:
            if df_smart[col].isnull().any():
                df_smart[col] = df_smart[col].fillna(
                    df_smart[col].mode().iloc[0]
                    if not df_smart[col].mode().empty
                    else "Unknown"
                )

        return df_smart

    def encode_categorical_features(
        self, df: pd.DataFrame, method: str = "onehot"
    ) -> pd.DataFrame:
        """Encode categorical features"""
        df_encoded = df.copy()

        categorical_columns = df_encoded.select_dtypes(
            include=["category", "object"]
        ).columns

        if method == "onehot":
            # One-hot encoding
            df_encoded = pd.get_dummies(
                df_encoded, columns=categorical_columns, prefix=categorical_columns
            )

        elif method == "label":
            # Label encoding
            for col in categorical_columns:
                if col not in self.encoders:
                    self.encoders[col] = LabelEncoder()
                    df_encoded[col] = self.encoders[col].fit_transform(
                        df_encoded[col].astype(str)
                    )
                else:
                    df_encoded[col] = self.encoders[col].transform(
                        df_encoded[col].astype(str)
                    )

        return df_encoded

    def scale_features(
        self,
        df: pd.DataFrame,
        method: str = "standard",
        exclude_columns: List[str] = None,
    ) -> pd.DataFrame:
        """Scale numerical features"""
        df_scaled = df.copy()
        exclude_columns = exclude_columns or []

        # Get numeric columns to scale
        numeric_columns = df_scaled.select_dtypes(include=[np.number]).columns
        columns_to_scale = [
            col for col in numeric_columns if col not in exclude_columns
        ]

        if len(columns_to_scale) == 0:
            return df_scaled

        # Choose scaler
        if method == "standard":
            if "standard_scaler" not in self.scalers:
                self.scalers["standard_scaler"] = StandardScaler()
                df_scaled[columns_to_scale] = self.scalers[
                    "standard_scaler"
                ].fit_transform(df_scaled[columns_to_scale])
            else:
                df_scaled[columns_to_scale] = self.scalers["standard_scaler"].transform(
                    df_scaled[columns_to_scale]
                )

        elif method == "minmax":
            if "minmax_scaler" not in self.scalers:
                self.scalers["minmax_scaler"] = MinMaxScaler()
                df_scaled[columns_to_scale] = self.scalers[
                    "minmax_scaler"
                ].fit_transform(df_scaled[columns_to_scale])
            else:
                df_scaled[columns_to_scale] = self.scalers["minmax_scaler"].transform(
                    df_scaled[columns_to_scale]
                )

        return df_scaled

    def create_interaction_features(self, df: pd.DataFrame) -> pd.DataFrame:
        """Create interaction features that might be important for home run prediction"""
        df_interactions = df.copy()

        # Define important interactions
        interactions = [
            ("avg_exit_velocity", "ballpark_hr_factor"),
            ("temperature", "ballpark_hr_factor"),
            ("wind_speed", "wind_helping_hrs"),
            ("career_hr_rate", "pitcher_hr_per_9"),
            ("recent_hr_rate", "pitcher_recent_hr_per_9"),
            ("hard_hit_rate", "ballpark_hr_factor"),
            ("age", "rest_days"),
            ("leverage_score", "clutch_hr_rate"),
        ]

        for feature1, feature2 in interactions:
            if (
                feature1 in df_interactions.columns
                and feature2 in df_interactions.columns
            ):
                interaction_name = f"{feature1}_x_{feature2}"
                df_interactions[interaction_name] = (
                    df_interactions[feature1] * df_interactions[feature2]
                )

        return df_interactions

    def prepare_features_for_modeling(
        self,
        df: pd.DataFrame,
        target_column: str = "home_run",
        test_size: float = 0.2,
        random_state: int = 42,
    ) -> Tuple[pd.DataFrame, pd.DataFrame, pd.Series, pd.Series]:
        """Complete preprocessing pipeline for modeling"""

        # Clean data
        df_clean = self.clean_data(df)

        # Handle missing values
        df_imputed = self.handle_missing_values(df_clean)

        # Encode categorical features
        df_encoded = self.encode_categorical_features(df_imputed)

        # Create interaction features
        df_interactions = self.create_interaction_features(df_encoded)

        # Scale features (exclude target column)
        exclude_cols = (
            [target_column] if target_column in df_interactions.columns else []
        )
        df_scaled = self.scale_features(
            df_interactions, exclude_columns=exclude_cols)

        # Separate features and target
        if target_column in df_scaled.columns:
            X = df_scaled.drop(columns=[target_column])
            y = df_scaled[target_column]

            # Ensure target is binary (0 or 1) for classification
            if y is not None:
                y = y.astype(int)
                # Verify binary values
                unique_values = y.unique()
                if not all(val in [0, 1] for val in unique_values):
                    print(
                        f"Warning: Target variable contains non-binary values: {unique_values}"
                    )
                    # Convert to binary if needed
                    y = (y > 0).astype(int)
        else:
            X = df_scaled
            y = None

        # Split data if target is available
        if y is not None:
            from sklearn.model_selection import train_test_split

            # Check if we can stratify (need at least 2 samples of each class)
            unique_classes = y.unique()
            min_class_count = y.value_counts().min()

            if len(unique_classes) > 1 and min_class_count >= 2:
                # Safe to stratify
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=test_size, random_state=random_state, stratify=y
                )
            else:
                # Cannot stratify safely - do regular split
                print(
                    f"Warning: Cannot stratify - min class count: {min_class_count}")
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=test_size, random_state=random_state
                )

            print(f"Training set home run rate: {y_train.mean():.3f}")
            print(f"Test set home run rate: {y_test.mean():.3f}")

            return X_train, X_test, y_train, y_test
        else:
            return X, None, None, None

    def get_feature_importance_stats(self, df: pd.DataFrame) -> Dict[str, Dict]:
        """Calculate feature statistics for importance analysis"""
        stats = {}

        numeric_columns = df.select_dtypes(include=[np.number]).columns

        for col in numeric_columns:
            stats[col] = {
                "mean": df[col].mean(),
                "std": df[col].std(),
                "min": df[col].min(),
                "max": df[col].max(),
                "missing_pct": df[col].isnull().mean() * 100,
                "unique_values": df[col].nunique(),
            }

        return stats
