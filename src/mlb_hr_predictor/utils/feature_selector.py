"""
Feature selection utilities for MLB Home Run Predictor
"""

import pandas as pd
import numpy as np
from typing import List, Dict, Tuple, Optional
from sklearn.feature_selection import SelectKBest, f_classif, mutual_info_classif, RFE
from sklearn.ensemble import RandomForestClassifier
from sklearn.linear_model import LogisticRegression
import warnings

warnings.filterwarnings("ignore")


class FeatureSelector:
    """Handles feature selection and importance analysis"""

    def __init__(self):
        self.selected_features = {}
        self.feature_scores = {}

    def select_features_univariate(
        self, X: pd.DataFrame, y: pd.Series, k: int = 50, score_func=f_classif
    ) -> List[str]:
        """Select features using univariate statistical tests"""
        selector = SelectKBest(score_func=score_func, k=k)
        X_selected = selector.fit_transform(X, y)

        # Get selected feature names
        selected_mask = selector.get_support()
        selected_features = X.columns[selected_mask].tolist()

        # Store feature scores
        feature_scores = dict(zip(X.columns, selector.scores_))
        self.feature_scores["univariate"] = feature_scores
        self.selected_features["univariate"] = selected_features

        return selected_features

    def select_features_mutual_info(
        self, X: pd.DataFrame, y: pd.Series, k: int = 50
    ) -> List[str]:
        """Select features using mutual information"""
        selector = SelectKBest(score_func=mutual_info_classif, k=k)
        X_selected = selector.fit_transform(X, y)

        # Get selected feature names
        selected_mask = selector.get_support()
        selected_features = X.columns[selected_mask].tolist()

        # Store feature scores
        feature_scores = dict(zip(X.columns, selector.scores_))
        self.feature_scores["mutual_info"] = feature_scores
        self.selected_features["mutual_info"] = selected_features

        return selected_features

    def select_features_rfe(
        self, X: pd.DataFrame, y: pd.Series, n_features: int = 50, estimator=None
    ) -> List[str]:
        """Select features using Recursive Feature Elimination"""
        if estimator is None:
            estimator = LogisticRegression(random_state=42, max_iter=1000)

        selector = RFE(estimator=estimator, n_features_to_select=n_features)
        X_selected = selector.fit_transform(X, y)

        # Get selected feature names
        selected_mask = selector.get_support()
        selected_features = X.columns[selected_mask].tolist()

        # Store feature rankings
        feature_rankings = dict(zip(X.columns, selector.ranking_))
        self.feature_scores["rfe"] = feature_rankings
        self.selected_features["rfe"] = selected_features

        return selected_features

    def select_features_tree_importance(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        n_features: int = 50,
        n_estimators: int = 100,
    ) -> List[str]:
        """Select features using tree-based feature importance"""
        rf = RandomForestClassifier(n_estimators=n_estimators, random_state=42)
        rf.fit(X, y)

        # Get feature importances
        feature_importances = dict(zip(X.columns, rf.feature_importances_))

        # Sort features by importance
        sorted_features = sorted(
            feature_importances.items(), key=lambda x: x[1], reverse=True
        )
        selected_features = [feature for feature, _ in sorted_features[:n_features]]

        self.feature_scores["tree_importance"] = feature_importances
        self.selected_features["tree_importance"] = selected_features

        return selected_features

    def select_features_correlation(
        self, X: pd.DataFrame, y: pd.Series, threshold: float = 0.1
    ) -> List[str]:
        """Select features based on correlation with target"""
        # Calculate correlations
        correlations = X.corrwith(y).abs()

        # Select features above threshold
        selected_features = correlations[correlations >= threshold].index.tolist()

        # Store correlations
        self.feature_scores["correlation"] = correlations.to_dict()
        self.selected_features["correlation"] = selected_features

        return selected_features

    def remove_highly_correlated_features(
        self, X: pd.DataFrame, threshold: float = 0.95
    ) -> List[str]:
        """Remove features that are highly correlated with each other"""
        # Calculate correlation matrix
        corr_matrix = X.corr().abs()

        # Find pairs of highly correlated features
        upper_triangle = corr_matrix.where(
            np.triu(np.ones(corr_matrix.shape), k=1).astype(bool)
        )

        # Find features to drop
        features_to_drop = [
            column
            for column in upper_triangle.columns
            if any(upper_triangle[column] > threshold)
        ]

        # Return features to keep
        features_to_keep = [col for col in X.columns if col not in features_to_drop]

        return features_to_keep

    def select_baseball_domain_features(
        self, available_features: List[str]
    ) -> List[str]:
        """Select features based on baseball domain knowledge"""

        # Define feature categories by importance
        high_importance_features = [
            "career_hr_rate",
            "recent_hr_rate",
            "slugging_pct",
            "iso_power",
            "avg_exit_velocity",
            "hard_hit_rate",
            "barrel_rate",
            "pitcher_hr_per_9",
            "pitcher_fb_rate",
            "ballpark_hr_factor",
            "temperature",
            "wind_helping_hrs",
            "leverage_score",
        ]

        medium_importance_features = [
            "vs_lhp_hr_rate",
            "vs_rhp_hr_rate",
            "home_hr_rate",
            "away_hr_rate",
            "pitcher_recent_hr_per_9",
            "pitcher_velocity",
            "age",
            "rest_days",
            "inning",
            "score_diff",
            "outs",
            "runner_in_scoring_position",
            "hitters_count",
            "pitchers_count",
            "ballpark_elevation",
        ]

        low_importance_features = [
            "career_consistency",
            "games_played",
            "lineup_position",
            "humidity",
            "pressure",
            "is_day_game",
            "month",
        ]

        # Select features in order of importance
        selected_features = []

        for feature_list in [
            high_importance_features,
            medium_importance_features,
            low_importance_features,
        ]:
            for feature in feature_list:
                if feature in available_features and feature not in selected_features:
                    selected_features.append(feature)

        # Add any remaining features
        for feature in available_features:
            if feature not in selected_features:
                selected_features.append(feature)

        self.selected_features["domain_knowledge"] = selected_features

        return selected_features

    def combine_feature_selection_methods(
        self,
        X: pd.DataFrame,
        y: pd.Series,
        methods: List[str] = None,
        final_k: int = 50,
    ) -> List[str]:
        """Combine multiple feature selection methods"""
        if methods is None:
            methods = ["univariate", "mutual_info", "tree_importance", "correlation"]

        all_selected_features = []

        # Run each method
        for method in methods:
            if method == "univariate":
                features = self.select_features_univariate(X, y, k=final_k)
            elif method == "mutual_info":
                features = self.select_features_mutual_info(X, y, k=final_k)
            elif method == "tree_importance":
                features = self.select_features_tree_importance(
                    X, y, n_features=final_k
                )
            elif method == "correlation":
                features = self.select_features_correlation(X, y)
            elif method == "rfe":
                features = self.select_features_rfe(X, y, n_features=final_k)
            elif method == "domain_knowledge":
                features = self.select_baseball_domain_features(X.columns.tolist())

            all_selected_features.extend(features)

        # Count feature occurrences
        feature_counts = {}
        for feature in all_selected_features:
            feature_counts[feature] = feature_counts.get(feature, 0) + 1

        # Sort by frequency and select top features
        sorted_features = sorted(
            feature_counts.items(), key=lambda x: x[1], reverse=True
        )
        final_selected_features = [feature for feature, _ in sorted_features[:final_k]]

        self.selected_features["combined"] = final_selected_features

        return final_selected_features

    def get_feature_importance_summary(self) -> pd.DataFrame:
        """Get a summary of feature importance across all methods"""
        if not self.feature_scores:
            return pd.DataFrame()

        # Combine all scores into a DataFrame
        summary_data = {}

        for method, scores in self.feature_scores.items():
            if method == "rfe":
                # For RFE, convert rankings to importance (lower rank = higher importance)
                max_rank = max(scores.values())
                scores = {k: max_rank - v + 1 for k, v in scores.items()}

            summary_data[method] = scores

        summary_df = pd.DataFrame(summary_data)

        # Add mean importance across methods
        summary_df["mean_importance"] = summary_df.mean(axis=1)

        # Sort by mean importance
        summary_df = summary_df.sort_values("mean_importance", ascending=False)

        return summary_df

    def plot_feature_importance(self, method: str = "tree_importance", top_n: int = 20):
        """Plot feature importance (requires matplotlib)"""
        try:
            import matplotlib.pyplot as plt

            if method not in self.feature_scores:
                print(f"No scores available for method: {method}")
                return

            scores = self.feature_scores[method]

            # Get top N features
            sorted_features = sorted(scores.items(), key=lambda x: x[1], reverse=True)[
                :top_n
            ]
            features, importance_scores = zip(*sorted_features)

            # Create plot
            plt.figure(figsize=(10, 8))
            plt.barh(range(len(features)), importance_scores)
            plt.yticks(range(len(features)), features)
            plt.xlabel("Importance Score")
            plt.title(f'Top {top_n} Features - {method.replace("_", " ").title()}')
            plt.gca().invert_yaxis()
            plt.tight_layout()
            plt.show()

        except ImportError:
            print("Matplotlib not available for plotting")
