"""
Model utilities for MLB Home Run Predictor
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.metrics import (
    accuracy_score,
    precision_score,
    recall_score,
    f1_score,
    roc_auc_score,
    classification_report,
)
from sklearn.model_selection import cross_val_score, StratifiedKFold
import joblib
import warnings

warnings.filterwarnings("ignore")


class ModelUtils:
    """Utilities for model training, evaluation, and management"""

    def __init__(self):
        self.models = {}
        self.model_performance = {}

    def evaluate_model(
        self, model, X_test: pd.DataFrame, y_test: pd.Series, model_name: str = "model"
    ) -> Dict[str, float]:
        """Evaluate model performance with multiple metrics"""

        # Make predictions
        y_pred = model.predict(X_test)
        y_pred_proba = (
            model.predict_proba(X_test)[:, 1]
            if hasattr(model, "predict_proba")
            else None
        )

        # Calculate metrics
        metrics = {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred, average="binary"),
            "recall": recall_score(y_test, y_pred, average="binary"),
            "f1_score": f1_score(y_test, y_pred, average="binary"),
        }

        # Add AUC if probabilities are available
        if y_pred_proba is not None:
            metrics["roc_auc"] = roc_auc_score(y_test, y_pred_proba)

        # Store performance
        self.model_performance[model_name] = metrics

        return metrics

    def cross_validate_model(
        self,
        model,
        X: pd.DataFrame,
        y: pd.Series,
        cv_folds: int = 5,
        scoring: str = "roc_auc",
    ) -> Dict[str, float]:
        """Perform cross-validation on model"""

        cv = StratifiedKFold(n_splits=cv_folds, shuffle=True, random_state=42)
        cv_scores = cross_val_score(model, X, y, cv=cv, scoring=scoring)

        cv_results = {
            "mean_score": cv_scores.mean(),
            "std_score": cv_scores.std(),
            "min_score": cv_scores.min(),
            "max_score": cv_scores.max(),
            "scores": cv_scores.tolist(),
        }

        return cv_results

    def calculate_home_run_probability(
        self, model, features: Dict[str, float]
    ) -> float:
        """Calculate home run probability for a single at-bat"""

        # Convert features to DataFrame
        feature_df = pd.DataFrame([features])

        # Make prediction
        if hasattr(model, "predict_proba"):
            probability = model.predict_proba(feature_df)[0, 1]
        else:
            # For models without predict_proba, use decision function or predict
            if hasattr(model, "decision_function"):
                decision_score = model.decision_function(feature_df)[0]
                # Convert to probability using sigmoid
                probability = 1 / (1 + np.exp(-decision_score))
            else:
                # Binary prediction only
                prediction = model.predict(feature_df)[0]
                probability = float(prediction)

        return probability

    def get_feature_importance(
        self, model, feature_names: List[str]
    ) -> Dict[str, float]:
        """Get feature importance from trained model"""

        importance_dict = {}

        if hasattr(model, "feature_importances_"):
            # Tree-based models
            importances = model.feature_importances_
            importance_dict = dict(zip(feature_names, importances))

        elif hasattr(model, "coef_"):
            # Linear models
            coefficients = model.coef_[0] if len(model.coef_.shape) > 1 else model.coef_
            importance_dict = dict(zip(feature_names, np.abs(coefficients)))

        else:
            print(f"Feature importance not available for model type: {type(model)}")

        return importance_dict

    def save_model(self, model, model_name: str, filepath: str = None):
        """Save trained model to disk"""
        if filepath is None:
            filepath = f"models/{model_name}.joblib"

        # Create directory if it doesn't exist
        import os

        os.makedirs(os.path.dirname(filepath), exist_ok=True)

        # Save model
        joblib.dump(model, filepath)

        # Store in memory
        self.models[model_name] = model

        print(f"Model saved to {filepath}")

    def load_model(self, filepath: str, model_name: str = None):
        """Load trained model from disk"""
        model = joblib.load(filepath)

        if model_name is None:
            model_name = filepath.split("/")[-1].replace(".joblib", "")

        self.models[model_name] = model

        print(f"Model loaded from {filepath}")
        return model

    def compare_models(
        self, models_dict: Dict[str, Any], X_test: pd.DataFrame, y_test: pd.Series
    ) -> pd.DataFrame:
        """Compare performance of multiple models"""

        comparison_results = []

        for model_name, model in models_dict.items():
            metrics = self.evaluate_model(model, X_test, y_test, model_name)
            metrics["model_name"] = model_name
            comparison_results.append(metrics)

        # Convert to DataFrame
        comparison_df = pd.DataFrame(comparison_results)
        comparison_df = comparison_df.set_index("model_name")

        # Sort by a primary metric (e.g., ROC AUC)
        if "roc_auc" in comparison_df.columns:
            comparison_df = comparison_df.sort_values("roc_auc", ascending=False)
        elif "f1_score" in comparison_df.columns:
            comparison_df = comparison_df.sort_values("f1_score", ascending=False)

        return comparison_df

    def generate_prediction_report(
        self,
        model,
        X_test: pd.DataFrame,
        y_test: pd.Series,
        feature_names: List[str] = None,
    ) -> Dict[str, Any]:
        """Generate comprehensive prediction report"""

        # Basic predictions
        y_pred = model.predict(X_test)
        y_pred_proba = (
            model.predict_proba(X_test)[:, 1]
            if hasattr(model, "predict_proba")
            else None
        )

        # Performance metrics
        metrics = self.evaluate_model(model, X_test, y_test)

        # Classification report
        class_report = classification_report(y_test, y_pred, output_dict=True)

        # Feature importance
        feature_importance = {}
        if feature_names:
            feature_importance = self.get_feature_importance(model, feature_names)

        # Prediction distribution
        pred_distribution = {
            "total_predictions": len(y_pred),
            "predicted_home_runs": int(y_pred.sum()),
            "actual_home_runs": int(y_test.sum()),
            "home_run_rate_predicted": y_pred.mean(),
            "home_run_rate_actual": y_test.mean(),
        }

        if y_pred_proba is not None:
            pred_distribution.update(
                {
                    "avg_probability": y_pred_proba.mean(),
                    "min_probability": y_pred_proba.min(),
                    "max_probability": y_pred_proba.max(),
                    "std_probability": y_pred_proba.std(),
                }
            )

        report = {
            "metrics": metrics,
            "classification_report": class_report,
            "feature_importance": feature_importance,
            "prediction_distribution": pred_distribution,
            "predictions": y_pred.tolist(),
            "probabilities": (
                y_pred_proba.tolist() if y_pred_proba is not None else None
            ),
        }

        return report

    def calculate_expected_home_runs(self, model, X: pd.DataFrame) -> float:
        """Calculate expected number of home runs for a set of at-bats"""

        if hasattr(model, "predict_proba"):
            probabilities = model.predict_proba(X)[:, 1]
            expected_hrs = probabilities.sum()
        else:
            predictions = model.predict(X)
            expected_hrs = predictions.sum()

        return expected_hrs

    def get_model_summary(self) -> pd.DataFrame:
        """Get summary of all stored models and their performance"""

        if not self.model_performance:
            return pd.DataFrame()

        summary_df = pd.DataFrame(self.model_performance).T

        return summary_df

    def predict_game_home_runs(self, model, game_data: pd.DataFrame) -> Dict[str, Any]:
        """Predict home runs for an entire game"""

        # Get probabilities for each at-bat
        if hasattr(model, "predict_proba"):
            probabilities = model.predict_proba(game_data)[:, 1]
        else:
            probabilities = model.predict(game_data).astype(float)

        # Calculate game-level statistics
        game_predictions = {
            "total_at_bats": len(game_data),
            "expected_home_runs": probabilities.sum(),
            "avg_hr_probability": probabilities.mean(),
            "max_hr_probability": probabilities.max(),
            "min_hr_probability": probabilities.min(),
            "at_bats_over_10_percent": (probabilities > 0.10).sum(),
            "at_bats_over_20_percent": (probabilities > 0.20).sum(),
            "at_bats_over_30_percent": (probabilities > 0.30).sum(),
            "individual_probabilities": probabilities.tolist(),
        }

        return game_predictions
