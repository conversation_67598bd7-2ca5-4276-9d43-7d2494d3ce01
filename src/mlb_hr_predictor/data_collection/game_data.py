"""
Game data collection module for contextual information
"""

import pandas as pd
from pybaseball import schedule_and_record
from typing import Dict, List, Optional
import warnings

warnings.filterwarnings("ignore")


class GameDataCollector:
    """Collects game-level data and contextual information"""

    def __init__(self):
        self.cache = {}

    def get_schedule_data(
        self, season: int, team: Optional[str] = None
    ) -> pd.DataFrame:
        """Get schedule data for a season"""
        try:
            print(f"Collecting schedule data for {season}")
            if team:
                schedule = schedule_and_record(season, team)
            else:
                # Get all teams' schedules
                schedule_data = []
                teams = self.get_team_list()
                for team_abbr in teams:
                    try:
                        team_schedule = schedule_and_record(
                            season, team_abbr)
                        team_schedule["team"] = team_abbr
                        schedule_data.append(team_schedule)
                    except:
                        continue
                schedule = (
                    pd.concat(schedule_data, ignore_index=True)
                    if schedule_data
                    else pd.DataFrame()
                )

            return schedule
        except Exception as e:
            print(f"Error collecting schedule data: {e}")
            return pd.DataFrame()

    def get_team_list(self) -> List[str]:
        """Get list of MLB team abbreviations"""
        return [
            "LAA",
            "HOU",
            "OAK",
            "TOR",
            "ATL",
            "MIL",
            "STL",
            "CHC",
            "ARI",
            "LAD",
            "SF",
            "CLE",
            "SEA",
            "MIA",
            "NYM",
            "WSH",
            "BAL",
            "SD",
            "PHI",
            "PIT",
            "TEX",
            "TB",
            "BOS",
            "CIN",
            "COL",
            "KC",
            "DET",
            "MIN",
            "CWS",
            "NYY",
        ]

    def get_ballpark_info(self) -> Dict:
        """Get ballpark information and factors"""

        return {
            # Most hitter-friendly parks
            'Coors Field': {
                'team': 'COL', 'elevation': 5200, 'hr_factor': 1.50,
                'dimensions': {'lf': 347, 'cf': 415, 'rf': 350},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 1995
            },
            'Yankee Stadium': {
                'team': 'NYY', 'elevation': 55, 'hr_factor': 1.20,
                'dimensions': {'lf': 318, 'cf': 408, 'rf': 314},
                'wall_height': {'lf': 6, 'cf': 6, 'rf': 10},
                'foul_territory': 'small', 'opened': 2009
            },
            'Citizens Bank Park': {
                'team': 'PHI', 'elevation': 20, 'hr_factor': 1.10,
                'dimensions': {'lf': 329, 'cf': 401, 'rf': 330},
                'wall_height': {'lf': 6, 'cf': 6, 'rf': 6},
                'foul_territory': 'small', 'opened': 2004
            },
            'Wrigley Field': {
                'team': 'CHC', 'elevation': 600, 'hr_factor': 1.10,
                'dimensions': {'lf': 355, 'cf': 400, 'rf': 353},
                'wall_height': {'lf': 11, 'cf': 11, 'rf': 11},
                'foul_territory': 'small', 'opened': 1914
            },
            'Fenway Park': {
                'team': 'BOS', 'elevation': 21, 'hr_factor': 1.05,
                'dimensions': {'lf': 310, 'cf': 420, 'rf': 302},
                'wall_height': {'lf': 37, 'cf': 17, 'rf': 3},
                'foul_territory': 'small', 'opened': 1912
            },
            'Great American Ball Park': {
                'team': 'CIN', 'elevation': 550, 'hr_factor': 1.05,
                'dimensions': {'lf': 328, 'cf': 404, 'rf': 325},
                'wall_height': {'lf': 12, 'cf': 12, 'rf': 12},
                'foul_territory': 'average', 'opened': 2003
            },
            'Oriole Park at Camden Yards': {
                'team': 'BAL', 'elevation': 20, 'hr_factor': 1.05,
                'dimensions': {'lf': 333, 'cf': 400, 'rf': 318},
                'wall_height': {'lf': 7, 'cf': 7, 'rf': 25},
                'foul_territory': 'small', 'opened': 1992
            },
            'Rogers Centre': {
                'team': 'TOR', 'elevation': 300, 'hr_factor': 1.05,
                'dimensions': {'lf': 328, 'cf': 400, 'rf': 328},
                'wall_height': {'lf': 10, 'cf': 10, 'rf': 10},
                'foul_territory': 'average', 'opened': 1989
            },
            'Guaranteed Rate Field': {
                'team': 'CWS', 'elevation': 595, 'hr_factor': 1.05,
                'dimensions': {'lf': 330, 'cf': 400, 'rf': 335},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 1991
            },

            # Neutral parks
            'Truist Park': {
                'team': 'ATL', 'elevation': 1050, 'hr_factor': 1.00,
                'dimensions': {'lf': 335, 'cf': 400, 'rf': 325},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2017
            },
            'Globe Life Field': {
                'team': 'TEX', 'elevation': 551, 'hr_factor': 1.00,
                'dimensions': {'lf': 329, 'cf': 407, 'rf': 326},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2020
            },
            'Target Field': {
                'team': 'MIN', 'elevation': 815, 'hr_factor': 1.00,
                'dimensions': {'lf': 339, 'cf': 411, 'rf': 328},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2010
            },
            'Nationals Park': {
                'team': 'WSH', 'elevation': 15, 'hr_factor': 1.00,
                'dimensions': {'lf': 336, 'cf': 402, 'rf': 335},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2008
            },

            # Pitcher-friendly parks
            'American Family Field': {
                'team': 'MIL', 'elevation': 635, 'hr_factor': 0.95,
                'dimensions': {'lf': 344, 'cf': 400, 'rf': 345},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2001
            },
            'PNC Park': {
                'team': 'PIT', 'elevation': 730, 'hr_factor': 0.95,
                'dimensions': {'lf': 325, 'cf': 399, 'rf': 320},
                'wall_height': {'lf': 6, 'cf': 6, 'rf': 21},
                'foul_territory': 'average', 'opened': 2001
            },
            'Angel Stadium': {
                'team': 'LAA', 'elevation': 160, 'hr_factor': 0.95,
                'dimensions': {'lf': 330, 'cf': 400, 'rf': 330},
                'wall_height': {'lf': 18, 'cf': 18, 'rf': 18},
                'foul_territory': 'large', 'opened': 1966
            },
            'Progressive Field': {
                'team': 'CLE', 'elevation': 660, 'hr_factor': 0.95,
                'dimensions': {'lf': 325, 'cf': 405, 'rf': 325},
                'wall_height': {'lf': 19, 'cf': 19, 'rf': 19},
                'foul_territory': 'average', 'opened': 1994
            },
            'loanDepot park': {
                'team': 'MIA', 'elevation': 10, 'hr_factor': 0.95,
                'dimensions': {'lf': 344, 'cf': 407, 'rf': 335},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2012
            },
            'Busch Stadium': {
                'team': 'STL', 'elevation': 465, 'hr_factor': 0.90,
                'dimensions': {'lf': 336, 'cf': 400, 'rf': 335},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2006
            },
            'Minute Maid Park': {
                'team': 'HOU', 'elevation': 22, 'hr_factor': 0.90,
                'dimensions': {'lf': 315, 'cf': 436, 'rf': 326},
                'wall_height': {'lf': 19, 'cf': 19, 'rf': 19},
                'foul_territory': 'small', 'opened': 2000
            },
            'T-Mobile Park': {
                'team': 'SEA', 'elevation': 15, 'hr_factor': 0.90,
                'dimensions': {'lf': 331, 'cf': 401, 'rf': 326},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 1999
            },
            'Citi Field': {
                'team': 'NYM', 'elevation': 20, 'hr_factor': 0.90,
                'dimensions': {'lf': 335, 'cf': 408, 'rf': 330},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2009
            },
            'Kauffman Stadium': {
                'team': 'KC', 'elevation': 750, 'hr_factor': 0.90,
                'dimensions': {'lf': 330, 'cf': 410, 'rf': 330},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'large', 'opened': 1973
            },
            'Tropicana Field': {
                'team': 'TB', 'elevation': 15, 'hr_factor': 0.90,
                'dimensions': {'lf': 315, 'cf': 404, 'rf': 322},
                'wall_height': {'lf': 10, 'cf': 10, 'rf': 10},
                'foul_territory': 'large', 'opened': 1990
            },
            'Oakland Coliseum': {
                'team': 'OAK', 'elevation': 6, 'hr_factor': 0.85,
                'dimensions': {'lf': 330, 'cf': 400, 'rf': 330},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'large', 'opened': 1968
            },
            'Dodger Stadium': {
                'team': 'LAD', 'elevation': 340, 'hr_factor': 0.85,
                'dimensions': {'lf': 330, 'cf': 395, 'rf': 330},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 1962
            },
            'Petco Park': {
                'team': 'SD', 'elevation': 62, 'hr_factor': 0.85,
                'dimensions': {'lf': 336, 'cf': 396, 'rf': 322},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2004
            },
            'Comerica Park': {
                'team': 'DET', 'elevation': 585, 'hr_factor': 0.85,
                'dimensions': {'lf': 345, 'cf': 420, 'rf': 330},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 2000
            },
            'Oracle Park': {
                'team': 'SF', 'elevation': 12, 'hr_factor': 0.75,
                'dimensions': {'lf': 339, 'cf': 399, 'rf': 309},
                'wall_height': {'lf': 25, 'cf': 25, 'rf': 25},
                'foul_territory': 'large', 'opened': 2000
            },
            'Chase Field': {
                'team': 'ARI', 'elevation': 1100, 'hr_factor': 1.05,
                'dimensions': {'lf': 330, 'cf': 407, 'rf': 334},
                'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
                'foul_territory': 'average', 'opened': 1998
            }
        }

    def get_weather_data_placeholder(self, date: str, location: str) -> Dict:
        """Placeholder for weather data - would integrate with weather API"""
        return {
            "temperature": 75,
            "humidity": 60,
            "wind_speed": 5,
            "wind_direction": "out_to_rf",
            "pressure": 30.0,
            "conditions": "clear",
        }
