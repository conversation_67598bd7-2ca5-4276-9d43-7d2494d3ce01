"""
Player statistics collection module
"""

import pandas as pd
import pybaseball as pyb
from typing import List, Dict, Optional
import warnings

warnings.filterwarnings("ignore")


class PlayerStatsCollector:
    """Collects and processes player batting statistics"""

    def __init__(self):
        self.cache = {}

    def get_player_season_stats(self, season: int) -> pd.DataFrame:
        """Get batting stats for all players in a season"""
        if season in self.cache:
            return self.cache[season]

        print(f"Collecting batting stats for {season}...")
        try:
            # pybaseball provides comprehensive batting stats including Age, ISO, SLG, etc.
            stats = pyb.batting_stats(season, qual=50)  # Minimum 50 PA

            # Verify we have the columns we need
            required_cols = ['Name', 'Age', 'HR', 'AB', 'PA', 'SLG', 'AVG']
            missing_cols = [
                col for col in required_cols if col not in stats.columns]

            if missing_cols:
                print(
                    f"Warning: Missing columns in batting stats: {missing_cols}")

            # Add ISO if not present (pybaseball usually includes it)
            if 'ISO' not in stats.columns and 'SLG' in stats.columns and 'AVG' in stats.columns:
                stats['ISO'] = stats['SLG'] - stats['AVG']

            self.cache[season] = stats
            return stats

        except Exception as e:
            print(f"Error collecting batting stats for {season}: {str(e)}")
            return pd.DataFrame()

    def get_player_career_stats(
        self, player_id: str, start_season: int = 2015
    ) -> pd.DataFrame:
        """Get career stats for a specific player"""
        career_stats = []
        current_year = 2025

        for year in range(start_season, current_year + 1):
            try:
                season_stats = pyb.batting_stats(year, qual=1)
                player_stats = season_stats[season_stats["IDfg"] == player_id]
                if not player_stats.empty:
                    career_stats.append(player_stats)
            except Exception as e:
                print(f"Error getting stats for {year}: {e}")
                continue

        if career_stats:
            return pd.concat(career_stats, ignore_index=True)
        return pd.DataFrame()

    def get_player_splits(self, player_id: str, season: int) -> Dict:
        """Get situational splits for a player"""
        try:
            # Get splits data (vs LHP/RHP, home/away, etc.)
            splits_data = {}

            # This would require more specific pybaseball functions
            # For now, we'll return a placeholder structure
            splits_data = {
                "vs_lhp": {"HR": 0, "AB": 0, "PA": 0},
                "vs_rhp": {"HR": 0, "AB": 0, "PA": 0},
                "home": {"HR": 0, "AB": 0, "PA": 0},
                "away": {"HR": 0, "AB": 0, "PA": 0},
                "clutch": {"HR": 0, "AB": 0, "PA": 0},
            }

            return splits_data
        except Exception as e:
            print(f"Error getting splits for player {player_id}: {e}")
            return {}

    def get_recent_performance(self, player_id: str, games: int = 30) -> Dict:
        """Get recent performance metrics for a player"""
        try:
            # This would require game-by-game data
            # For now, return placeholder
            return {
                "recent_hr": 0,
                "recent_ab": 0,
                "recent_games": 0,
                "hot_streak": False,
                "days_since_last_hr": 0,
            }
        except Exception as e:
            print(f"Error getting recent performance for {player_id}: {e}")
            return {}

    def calculate_advanced_metrics(self, stats_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate advanced batting metrics"""
        df = stats_df.copy()

        # Home run rate
        df["hr_rate"] = df["HR"] / df["AB"]
        df["hr_per_pa"] = df["HR"] / df["PA"]

        # Isolated Power (ISO)
        df["iso"] = df["SLG"] - df["AVG"]

        # Contact quality metrics (if available)
        if "Hard%" in df.columns:
            df["hard_hit_rate"] = df["Hard%"] / 100

        if "Barrel%" in df.columns:
            df["barrel_rate"] = df["Barrel%"] / 100

        # Power metrics
        df["ab_per_hr"] = df["AB"] / \
            df["HR"].replace(0, 1)  # Avoid division by zero

        return df

    def get_player_info(self, player_name: str) -> Optional[Dict]:
        """Get player information including ID"""
        try:
            # Search for player
            player_info = pyb.playerid_lookup(
                player_name.split()[-1], player_name.split()[0]
            )
            if not player_info.empty:
                return player_info.iloc[0].to_dict()
            return None
        except Exception as e:
            print(f"Error looking up player {player_name}: {e}")
            return None
