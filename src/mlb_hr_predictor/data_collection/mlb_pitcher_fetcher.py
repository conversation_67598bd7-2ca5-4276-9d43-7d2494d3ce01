#!/usr/bin/env python3
"""
MLB Pitcher Fetcher - Gets real probable pitchers from MLB Stats API
"""

import requests
import logging
from typing import Dict
from datetime import datetime

logger = logging.getLogger(__name__)


class MLBPitcherFetcher:
    """Fetches probable pitchers from MLB Stats API"""

    def __init__(self):
        self.base_url = "https://statsapi.mlb.com/api/v1"
        self.session = requests.Session()

    def get_probable_pitchers_for_date(self, date: str) -> Dict[str, Dict[str, str]]:
        """Get all probable pitchers for games on a specific date"""
        try:
            url = f"{self.base_url}/schedule"
            params = {
                "sportId": 1,  # MLB
                "date": date,
                "hydrate": "probablePitcher,team",
            }

            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()

            data = response.json()
            pitchers_by_game = {}

            for date_data in data.get("dates", []):
                for game in date_data.get("games", []):
                    home_team_abbr = (
                        game.get("teams", {})
                        .get("home", {})
                        .get("team", {})
                        .get("abbreviation", "")
                    )
                    away_team_abbr = (
                        game.get("teams", {})
                        .get("away", {})
                        .get("team", {})
                        .get("abbreviation", "")
                    )

                    if home_team_abbr and away_team_abbr:
                        home_pitcher = (
                            game.get("teams", {})
                            .get("home", {})
                            .get("probablePitcher", {})
                            .get("fullName", "TBD")
                        )
                        away_pitcher = (
                            game.get("teams", {})
                            .get("away", {})
                            .get("probablePitcher", {})
                            .get("fullName", "TBD")
                        )

                        # Use a consistent key format: away@home (standard format)
                        matchup_key = f"{away_team_abbr}@{home_team_abbr}"
                        pitchers_by_game[matchup_key] = {
                            "home": home_pitcher,
                            "away": away_pitcher,
                            "home_team": home_team_abbr,
                            "away_team": away_team_abbr,
                        }

            logger.info(
                f"Found probable pitchers for {len(pitchers_by_game)} games on {date}"
            )
            return pitchers_by_game

        except Exception as e:
            logger.error(f"Error fetching probable pitchers for {date}: {e}")
            return {}

    def get_probable_pitchers_for_matchup(
        self, away_team: str, home_team: str, date: str = None
    ) -> Dict[str, str]:
        """Get probable pitchers for a specific team matchup"""
        if date is None:
            date = datetime.now().strftime("%Y-%m-%d")

        all_pitchers = self.get_probable_pitchers_for_date(date)
        matchup_key = f"{away_team}@{home_team}"

        if matchup_key in all_pitchers:
            return {
                "home": all_pitchers[matchup_key]["home"],
                "away": all_pitchers[matchup_key]["away"],
            }

        return {"home": "TBD", "away": "TBD"}

    def get_team_abbreviation_mapping(self) -> Dict[str, str]:
        """Get mapping of full team names to abbreviations"""
        return {
            "Arizona Diamondbacks": "ARI",
            "Atlanta Braves": "ATL",
            "Baltimore Orioles": "BAL",
            "Boston Red Sox": "BOS",
            "Chicago Cubs": "CHC",
            "Chicago White Sox": "CWS",
            "Cincinnati Reds": "CIN",
            "Cleveland Guardians": "CLE",
            "Colorado Rockies": "COL",
            "Detroit Tigers": "DET",
            "Houston Astros": "HOU",
            "Kansas City Royals": "KC",
            "Los Angeles Angels": "LAA",
            "Los Angeles Dodgers": "LAD",
            "Miami Marlins": "MIA",
            "Milwaukee Brewers": "MIL",
            "Minnesota Twins": "MIN",
            "New York Mets": "NYM",
            "New York Yankees": "NYY",
            "Oakland Athletics": "OAK",
            "Philadelphia Phillies": "PHI",
            "Pittsburgh Pirates": "PIT",
            "San Diego Padres": "SD",
            "San Francisco Giants": "SF",
            "Seattle Mariners": "SEA",
            "St. Louis Cardinals": "STL",
            "Tampa Bay Rays": "TB",
            "Texas Rangers": "TEX",
            "Toronto Blue Jays": "TOR",
            "Washington Nationals": "WSH",
        }


def get_real_probable_pitchers(
    away_team: str, home_team: str, date: str = None
) -> Dict[str, str]:
    """
    Convenience function to get probable pitchers for a matchup

    Args:
        away_team: Away team abbreviation (e.g., 'LAA')
        home_team: Home team abbreviation (e.g., 'HOU')
        date: Date in YYYY-MM-DD format (defaults to today)

    Returns:
        Dict with 'home' and 'away' pitcher names
    """
    fetcher = MLBPitcherFetcher()
    return fetcher.get_probable_pitchers_for_matchup(away_team, home_team, date)


if __name__ == "__main__":
    # Test the fetcher
    import sys

    if len(sys.argv) >= 3:
        away = sys.argv[1]
        home = sys.argv[2]
        date_arg = sys.argv[3] if len(sys.argv) > 3 else None

        pitchers = get_real_probable_pitchers(away, home, date_arg)
        print(f"{away} @ {home}: {pitchers['away']} vs {pitchers['home']}")
    else:
        # Test with today's games
        fetcher = MLBPitcherFetcher()
        today = datetime.now().strftime("%Y-%m-%d")
        all_pitchers = fetcher.get_probable_pitchers_for_date(today)

        print(f"Probable pitchers for {today}:")
        print("-" * 60)
        for matchup, pitchers in all_pitchers.items():
            away_team = pitchers["away_team"]
            home_team = pitchers["home_team"]
            away_pitcher = pitchers["away"]
            home_pitcher = pitchers["home"]
            print(f"{away_team} @ {home_team}: {away_pitcher} vs {home_pitcher}")
