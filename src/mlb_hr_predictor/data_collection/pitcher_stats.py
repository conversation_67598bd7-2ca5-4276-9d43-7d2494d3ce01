"""
Pitcher statistics collection module
"""

import pandas as pd
import pybaseball as pyb
from typing import Dict, Optional
import warnings

warnings.filterwarnings("ignore")


class PitcherStatsCollector:
    """Collects and processes pitcher statistics"""

    def __init__(self):
        self.cache = {}

    def get_pitcher_season_stats(self, season: int) -> pd.DataFrame:
        """Get pitching stats for all pitchers in a season"""
        if season in self.cache:
            return self.cache[season]

        print(f"Collecting pitching stats for {season}...")
        try:
            # pybaseball provides comprehensive pitching stats including Age, HR/9, etc.
            stats = pyb.pitching_stats(season, qual=50)  # Minimum 50 IP

            # Verify we have the columns we need
            required_cols = ['Name', 'Age', 'ERA',
                             'WHIP', 'HR/9', 'K/9', 'BB/9']
            missing_cols = [
                col for col in required_cols if col not in stats.columns]

            if missing_cols:
                print(
                    f"Warning: Missing columns in pitching stats: {missing_cols}")

            self.cache[season] = stats
            return stats

        except Exception as e:
            print(f"Error collecting pitching stats for {season}: {str(e)}")
            return pd.DataFrame()

    def get_pitcher_info(self, pitcher_name: str) -> Optional[Dict]:
        """Get pitcher information including ID"""
        try:
            # Search for pitcher
            pitcher_info = pyb.playerid_lookup(
                pitcher_name.split()[-1], pitcher_name.split()[0]
            )
            if not pitcher_info.empty:
                return pitcher_info.iloc[0].to_dict()
            return None
        except Exception as e:
            print(f"Error looking up pitcher {pitcher_name}: {e}")
            return None

    def get_pitcher_recent_stats(self, pitcher_id: str, games: int = 5) -> Dict:
        """Get recent performance for a pitcher"""
        try:
            # This would require game-by-game data
            # For now, return placeholder
            return {
                "recent_era": 0.0,
                "recent_whip": 0.0,
                "recent_hr_per_9": 0.0,
                "recent_k_per_9": 0.0,
                "recent_bb_per_9": 0.0,
            }
        except Exception as e:
            print(f"Error getting recent stats for pitcher {pitcher_id}: {e}")
            return {}

    def calculate_pitcher_metrics(self, stats_df: pd.DataFrame) -> pd.DataFrame:
        """Calculate advanced pitcher metrics relevant to home runs"""
        df = stats_df.copy()

        # Home run metrics
        df["hr_per_9"] = (df["HR"] * 9) / df["IP"]
        df["hr_per_fb"] = df["HR"] / (df.get("FB", 1))  # HR per fly ball

        # Contact quality allowed
        if "Hard%" in df.columns:
            df["hard_contact_allowed"] = df["Hard%"] / 100

        # Fly ball rate
        if "FB%" in df.columns:
            df["fb_rate"] = df["FB%"] / 100

        # Velocity metrics (if available)
        if "vFA (pfx)" in df.columns:
            df["avg_velocity"] = df["vFA (pfx)"]

        # Stuff metrics
        df["k_per_9"] = (df["SO"] * 9) / df["IP"]
        df["bb_per_9"] = (df["BB"] * 9) / df["IP"]
        df["k_bb_ratio"] = df["SO"] / df["BB"].replace(0, 1)

        return df

    def get_pitcher_vs_handedness(self, pitcher_id: str, season: int) -> Dict:
        """Get pitcher performance vs left/right handed batters"""
        try:
            # This would require splits data
            # For now, return placeholder
            return {
                "vs_lhb": {"HR": 0, "AB": 0, "PA": 0, "SLG": 0.0},
                "vs_rhb": {"HR": 0, "AB": 0, "PA": 0, "SLG": 0.0},
            }
        except Exception as e:
            print(
                f"Error getting handedness splits for pitcher {pitcher_id}: {e}")
            return {}

    def get_pitcher_ballpark_factors(self, pitcher_id: str, season: int) -> Dict:
        """Get pitcher performance in different ballparks"""
        try:
            # This would require game-by-game data with ballpark info
            return {
                "home_hr_rate": 0.0,
                "away_hr_rate": 0.0,
                "favorable_parks": [],
                "unfavorable_parks": [],
            }
        except Exception as e:
            print(
                f"Error getting ballpark factors for pitcher {pitcher_id}: {e}")
            return {}
