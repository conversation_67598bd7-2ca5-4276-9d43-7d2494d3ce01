"""
Statcast data collection module for advanced metrics
"""

import pandas as pd
import pybaseball as pyb
from datetime import datetime, timedelta
from typing import Dict, Optional, List
import warnings

warnings.filterwarnings("ignore")


class StatcastDataCollector:
    """Collects Statcast data for advanced metrics"""

    def __init__(self):
        self.cache = {}

    def get_statcast_batter_data(
        self, player_id: int, start_date: str, end_date: str
    ) -> pd.DataFrame:
        """Get Statcast data for a specific batter"""
        try:
            print(
                f"Collecting Statcast data for player {player_id} from {start_date} to {end_date}"
            )
            data = pyb.statcast_batter(start_date, end_date, player_id)
            return data
        except Exception as e:
            print(f"Error collecting Statcast batter data: {e}")
            return pd.DataFrame()

    def get_statcast_pitcher_data(
        self, player_id: int, start_date: str, end_date: str
    ) -> pd.DataFrame:
        """Get Statcast data for a specific pitcher"""
        try:
            print(
                f"Collecting Statcast data for pitcher {player_id} from {start_date} to {end_date}"
            )
            data = pyb.statcast_pitcher(start_date, end_date, player_id)
            return data
        except Exception as e:
            print(f"Error collecting Statcast pitcher data: {e}")
            return pd.DataFrame()

    def calculate_batter_statcast_metrics(self, statcast_df: pd.DataFrame) -> Dict:
        """Calculate advanced Statcast metrics for batters"""
        if statcast_df.empty:
            return {}

        metrics = {}

        # Exit velocity metrics
        if "launch_speed" in statcast_df.columns:
            metrics["avg_exit_velocity"] = statcast_df["launch_speed"].mean()
            metrics["max_exit_velocity"] = statcast_df["launch_speed"].max()
            metrics["hard_hit_rate"] = (statcast_df["launch_speed"] >= 95).mean()

        # Launch angle metrics
        if "launch_angle" in statcast_df.columns:
            metrics["avg_launch_angle"] = statcast_df["launch_angle"].mean()
            # Sweet spot (8-32 degrees)
            sweet_spot = (statcast_df["launch_angle"] >= 8) & (
                statcast_df["launch_angle"] <= 32
            )
            metrics["sweet_spot_rate"] = sweet_spot.mean()

        # Barrel rate
        if "barrel" in statcast_df.columns:
            metrics["barrel_rate"] = statcast_df["barrel"].mean()

        # Expected stats
        if "estimated_woba_using_speedangle" in statcast_df.columns:
            metrics["xwoba"] = statcast_df["estimated_woba_using_speedangle"].mean()

        if "estimated_ba_using_speedangle" in statcast_df.columns:
            metrics["xba"] = statcast_df["estimated_ba_using_speedangle"].mean()

        # Pull rate
        if "hit_location" in statcast_df.columns:
            # This would need more sophisticated logic to determine pull/center/oppo
            pass

        # Sprint speed (for context)
        if "sprint_speed" in statcast_df.columns:
            metrics["sprint_speed"] = statcast_df["sprint_speed"].mean()

        return metrics

    def calculate_pitcher_statcast_metrics(self, statcast_df: pd.DataFrame) -> Dict:
        """Calculate advanced Statcast metrics for pitchers"""
        if statcast_df.empty:
            return {}

        metrics = {}

        # Velocity metrics
        if "release_speed" in statcast_df.columns:
            metrics["avg_velocity"] = statcast_df["release_speed"].mean()
            metrics["max_velocity"] = statcast_df["release_speed"].max()

        # Contact quality allowed
        if "launch_speed" in statcast_df.columns:
            contact_data = statcast_df.dropna(subset=["launch_speed"])
            if not contact_data.empty:
                metrics["avg_exit_velo_allowed"] = contact_data["launch_speed"].mean()
                metrics["hard_contact_rate_allowed"] = (
                    contact_data["launch_speed"] >= 95
                ).mean()

        # Barrel rate allowed
        if "barrel" in statcast_df.columns:
            metrics["barrel_rate_allowed"] = statcast_df["barrel"].mean()

        # Expected stats allowed
        if "estimated_woba_using_speedangle" in statcast_df.columns:
            metrics["xwoba_allowed"] = statcast_df[
                "estimated_woba_using_speedangle"
            ].mean()

        return metrics

    def get_recent_statcast_trends(
        self, player_id: int, days: int = 30, player_type: str = "batter"
    ) -> Dict:
        """Get recent Statcast trends for a player"""
        end_date = datetime.now().strftime("%Y-%m-%d")
        start_date = (datetime.now() - timedelta(days=days)).strftime("%Y-%m-%d")

        if player_type == "batter":
            data = self.get_statcast_batter_data(player_id, start_date, end_date)
            return self.calculate_batter_statcast_metrics(data)
        else:
            data = self.get_statcast_pitcher_data(player_id, start_date, end_date)
            return self.calculate_pitcher_statcast_metrics(data)

    def get_home_run_characteristics(self, player_id: int, season: int) -> Dict:
        """Get characteristics of home runs hit by a player"""
        start_date = f"{season}-03-01"
        end_date = f"{season}-11-01"

        data = self.get_statcast_batter_data(player_id, start_date, end_date)

        if data.empty:
            return {}

        # Filter for home runs
        hr_data = data[data["events"] == "home_run"]

        if hr_data.empty:
            return {"total_hrs": 0}

        characteristics = {
            "total_hrs": len(hr_data),
            "avg_hr_distance": (
                hr_data["hit_distance_sc"].mean()
                if "hit_distance_sc" in hr_data.columns
                else None
            ),
            "avg_hr_exit_velo": (
                hr_data["launch_speed"].mean()
                if "launch_speed" in hr_data.columns
                else None
            ),
            "avg_hr_launch_angle": (
                hr_data["launch_angle"].mean()
                if "launch_angle" in hr_data.columns
                else None
            ),
        }

        return characteristics
