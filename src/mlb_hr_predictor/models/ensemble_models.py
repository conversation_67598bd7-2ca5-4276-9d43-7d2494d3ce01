"""
Ensemble models for improved home run prediction
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.ensemble import VotingClassifier, StackingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.model_selection import cross_val_score
import warnings

warnings.filterwarnings("ignore")


class EnsemblePredictor:
    """Ensemble methods for combining multiple models"""

    def __init__(self):
        self.ensemble_models = {}
        self.base_models = {}
        self.ensemble_performance = {}

    def create_voting_ensemble(
        self, base_models: Dict[str, Any], voting: str = "soft"
    ) -> VotingClassifier:
        """Create a voting ensemble from base models"""

        # Prepare estimators list
        estimators = [(name, model) for name, model in base_models.items()]

        # Create voting classifier
        voting_ensemble = VotingClassifier(
            estimators=estimators,
            voting=voting,  # 'soft' for probability voting, 'hard' for majority voting
        )

        self.ensemble_models["voting"] = voting_ensemble
        self.base_models.update(base_models)

        return voting_ensemble

    def create_stacking_ensemble(
        self, base_models: Dict[str, Any], meta_learner=None
    ) -> StackingClassifier:
        """Create a stacking ensemble with a meta-learner"""

        if meta_learner is None:
            meta_learner = LogisticRegression(random_state=42)

        # Prepare estimators list
        estimators = [(name, model) for name, model in base_models.items()]

        # Create stacking classifier
        stacking_ensemble = StackingClassifier(
            estimators=estimators,
            final_estimator=meta_learner,
            cv=5,  # Cross-validation folds for generating meta-features
            stack_method="predict_proba",  # Use probabilities as meta-features
        )

        self.ensemble_models["stacking"] = stacking_ensemble
        self.base_models.update(base_models)

        return stacking_ensemble

    def create_weighted_ensemble(
        self, base_models: Dict[str, Any], weights: Dict[str, float] = None
    ) -> "WeightedEnsemble":
        """Create a custom weighted ensemble"""

        if weights is None:
            # Equal weights
            weights = {name: 1.0 / len(base_models) for name in base_models.keys()}

        # Normalize weights
        total_weight = sum(weights.values())
        normalized_weights = {
            name: weight / total_weight for name, weight in weights.items()
        }

        weighted_ensemble = WeightedEnsemble(base_models, normalized_weights)

        self.ensemble_models["weighted"] = weighted_ensemble
        self.base_models.update(base_models)

        return weighted_ensemble

    def train_ensemble(
        self, ensemble_type: str, X_train: pd.DataFrame, y_train: pd.Series
    ) -> Any:
        """Train a specific ensemble model"""

        if ensemble_type not in self.ensemble_models:
            print(f"Ensemble type '{ensemble_type}' not found")
            return None

        ensemble_model = self.ensemble_models[ensemble_type]

        print(f"Training {ensemble_type} ensemble...")
        ensemble_model.fit(X_train, y_train)

        print(f"✓ {ensemble_type} ensemble trained successfully")
        return ensemble_model

    def evaluate_ensemble(
        self, ensemble_type: str, X_test: pd.DataFrame, y_test: pd.Series
    ) -> Dict[str, float]:
        """Evaluate ensemble performance"""

        if ensemble_type not in self.ensemble_models:
            print(f"Ensemble type '{ensemble_type}' not found")
            return {}

        ensemble_model = self.ensemble_models[ensemble_type]

        # Make predictions
        y_pred = ensemble_model.predict(X_test)

        if hasattr(ensemble_model, "predict_proba"):
            y_pred_proba = ensemble_model.predict_proba(X_test)[:, 1]
        else:
            y_pred_proba = None

        # Calculate metrics
        from sklearn.metrics import (
            accuracy_score,
            precision_score,
            recall_score,
            f1_score,
            roc_auc_score,
        )

        metrics = {
            "accuracy": accuracy_score(y_test, y_pred),
            "precision": precision_score(y_test, y_pred),
            "recall": recall_score(y_test, y_pred),
            "f1_score": f1_score(y_test, y_pred),
        }

        if y_pred_proba is not None:
            metrics["roc_auc"] = roc_auc_score(y_test, y_pred_proba)

        self.ensemble_performance[ensemble_type] = metrics

        return metrics

    def cross_validate_ensemble(
        self, ensemble_type: str, X: pd.DataFrame, y: pd.Series, cv: int = 5
    ) -> Dict[str, float]:
        """Cross-validate ensemble performance"""

        if ensemble_type not in self.ensemble_models:
            print(f"Ensemble type '{ensemble_type}' not found")
            return {}

        ensemble_model = self.ensemble_models[ensemble_type]

        # Perform cross-validation
        cv_scores = cross_val_score(ensemble_model, X, y, cv=cv, scoring="roc_auc")

        cv_results = {
            "mean_score": cv_scores.mean(),
            "std_score": cv_scores.std(),
            "min_score": cv_scores.min(),
            "max_score": cv_scores.max(),
        }

        return cv_results

    def predict_with_ensemble(
        self, ensemble_type: str, features: Dict[str, float]
    ) -> float:
        """Make prediction using ensemble model"""

        if ensemble_type not in self.ensemble_models:
            print(f"Ensemble type '{ensemble_type}' not found")
            return 0.0

        ensemble_model = self.ensemble_models[ensemble_type]

        # Convert features to DataFrame
        feature_df = pd.DataFrame([features])

        # Make prediction
        if hasattr(ensemble_model, "predict_proba"):
            probability = ensemble_model.predict_proba(feature_df)[0, 1]
        else:
            probability = float(ensemble_model.predict(feature_df)[0])

        return probability

    def get_ensemble_feature_importance(
        self, ensemble_type: str, feature_names: List[str]
    ) -> Dict[str, float]:
        """Get feature importance from ensemble model"""

        if ensemble_type not in self.ensemble_models:
            return {}

        ensemble_model = self.ensemble_models[ensemble_type]

        if ensemble_type == "voting":
            # Average feature importance across base models
            all_importances = {}

            for name, model in self.base_models.items():
                if hasattr(model, "feature_importances_"):
                    importances = dict(zip(feature_names, model.feature_importances_))
                elif hasattr(model, "coef_"):
                    coefficients = (
                        model.coef_[0] if len(model.coef_.shape) > 1 else model.coef_
                    )
                    importances = dict(zip(feature_names, np.abs(coefficients)))
                else:
                    continue

                for feature, importance in importances.items():
                    all_importances[feature] = (
                        all_importances.get(feature, 0) + importance
                    )

            # Average the importances
            num_models = len(
                [
                    m
                    for m in self.base_models.values()
                    if hasattr(m, "feature_importances_") or hasattr(m, "coef_")
                ]
            )

            if num_models > 0:
                avg_importances = {
                    k: v / num_models for k, v in all_importances.items()
                }
                return avg_importances

        elif ensemble_type == "stacking":
            # Use meta-learner feature importance if available
            meta_learner = ensemble_model.final_estimator_
            if hasattr(meta_learner, "coef_"):
                # For stacking, features are predictions from base models
                base_model_names = list(self.base_models.keys())
                coefficients = (
                    meta_learner.coef_[0]
                    if len(meta_learner.coef_.shape) > 1
                    else meta_learner.coef_
                )
                return dict(zip(base_model_names, np.abs(coefficients)))

        return {}

    def compare_ensemble_performance(self) -> pd.DataFrame:
        """Compare performance of all ensemble models"""

        if not self.ensemble_performance:
            return pd.DataFrame()

        comparison_df = pd.DataFrame(self.ensemble_performance).T

        # Sort by ROC AUC if available
        if "roc_auc" in comparison_df.columns:
            comparison_df = comparison_df.sort_values("roc_auc", ascending=False)
        elif "f1_score" in comparison_df.columns:
            comparison_df = comparison_df.sort_values("f1_score", ascending=False)

        return comparison_df


class WeightedEnsemble:
    """Custom weighted ensemble class"""

    def __init__(self, models: Dict[str, Any], weights: Dict[str, float]):
        self.models = models
        self.weights = weights
        self.is_fitted = False

    def fit(self, X: pd.DataFrame, y: pd.Series):
        """Fit all base models"""
        for name, model in self.models.items():
            if not hasattr(model, "predict"):
                # Model needs to be fitted
                model.fit(X, y)

        self.is_fitted = True
        return self

    def predict_proba(self, X: pd.DataFrame) -> np.ndarray:
        """Predict probabilities using weighted average"""
        if not self.is_fitted:
            raise ValueError("Ensemble must be fitted before making predictions")

        weighted_probas = None

        for name, model in self.models.items():
            if hasattr(model, "predict_proba"):
                probas = model.predict_proba(X)
                if weighted_probas is None:
                    weighted_probas = probas * self.weights[name]
                else:
                    weighted_probas += probas * self.weights[name]
            else:
                # For models without predict_proba, use binary predictions
                predictions = model.predict(X).reshape(-1, 1)
                binary_probas = np.column_stack([1 - predictions, predictions])

                if weighted_probas is None:
                    weighted_probas = binary_probas * self.weights[name]
                else:
                    weighted_probas += binary_probas * self.weights[name]

        return weighted_probas

    def predict(self, X: pd.DataFrame) -> np.ndarray:
        """Make binary predictions"""
        probas = self.predict_proba(X)
        return (probas[:, 1] > 0.5).astype(int)
