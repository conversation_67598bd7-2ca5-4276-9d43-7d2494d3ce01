"""
Model training module for MLB Home Run Predictor
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Tuple, Any, Optional
from sklearn.ensemble import RandomForestClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import GridSearchCV, RandomizedSearchCV
from sklearn.metrics import make_scorer, roc_auc_score
import xgboost as xgb
import lightgbm as lgb
import warnings

warnings.filterwarnings("ignore")


class ModelTrainer:
    """Handles training of various machine learning models for home run prediction"""

    def __init__(self):
        self.trained_models = {}
        self.best_params = {}
        self.training_history = {}

    def train_logistic_regression(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> LogisticRegression:
        """Train logistic regression model"""

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "C": [0.001, 0.01, 0.1, 1, 10, 100],
                "penalty": ["l1", "l2"],
                "solver": ["liblinear", "saga"],
                "max_iter": [1000, 2000],
            }

            lr = LogisticRegression(random_state=42)
            grid_search = GridSearchCV(
                lr, param_grid, cv=5, scoring="roc_auc", n_jobs=-1
            )
            grid_search.fit(X_train, y_train)

            best_model = grid_search.best_estimator_
            self.best_params["logistic_regression"] = grid_search.best_params_
        else:
            # Default parameters
            best_model = LogisticRegression(
                C=1.0, penalty="l2", solver="liblinear", max_iter=1000, random_state=42
            )
            best_model.fit(X_train, y_train)

        self.trained_models["logistic_regression"] = best_model
        return best_model

    def train_random_forest(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> RandomForestClassifier:
        """Train random forest model"""

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "n_estimators": [100, 200, 300],
                "max_depth": [10, 20, 30, None],
                "min_samples_split": [2, 5, 10],
                "min_samples_leaf": [1, 2, 4],
                "max_features": ["sqrt", "log2", None],
            }

            rf = RandomForestClassifier(random_state=42)

            # Use RandomizedSearchCV for efficiency
            random_search = RandomizedSearchCV(
                rf,
                param_grid,
                n_iter=50,
                cv=5,
                scoring="roc_auc",
                n_jobs=-1,
                random_state=42,
            )
            random_search.fit(X_train, y_train)

            best_model = random_search.best_estimator_
            self.best_params["random_forest"] = random_search.best_params_
        else:
            # Default parameters
            best_model = RandomForestClassifier(
                n_estimators=200,
                max_depth=20,
                min_samples_split=5,
                min_samples_leaf=2,
                max_features="sqrt",
                random_state=42,
            )
            best_model.fit(X_train, y_train)

        self.trained_models["random_forest"] = best_model
        return best_model

    def train_gradient_boosting(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> GradientBoostingClassifier:
        """Train gradient boosting model"""

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "n_estimators": [100, 200, 300],
                "learning_rate": [0.01, 0.1, 0.2],
                "max_depth": [3, 5, 7],
                "min_samples_split": [2, 5, 10],
                "min_samples_leaf": [1, 2, 4],
                "subsample": [0.8, 0.9, 1.0],
            }

            gb = GradientBoostingClassifier(random_state=42)

            random_search = RandomizedSearchCV(
                gb,
                param_grid,
                n_iter=50,
                cv=5,
                scoring="roc_auc",
                n_jobs=-1,
                random_state=42,
            )
            random_search.fit(X_train, y_train)

            best_model = random_search.best_estimator_
            self.best_params["gradient_boosting"] = random_search.best_params_
        else:
            # Default parameters
            best_model = GradientBoostingClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                min_samples_split=5,
                min_samples_leaf=2,
                subsample=0.9,
                random_state=42,
            )
            best_model.fit(X_train, y_train)

        self.trained_models["gradient_boosting"] = best_model
        return best_model

    def train_xgboost(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> xgb.XGBClassifier:
        """Train XGBoost model"""

        # Check class distribution and calculate base_score
        pos_rate = y_train.mean()
        if pos_rate == 0 or pos_rate == 1:
            print(
                f"Warning: Imbalanced dataset with positive rate: {pos_rate:.3f}")
            # Use a higher base score to make model less conservative
            # Increased from 0.05 to 0.08
            base_score = max(0.05, min(0.99, pos_rate)
                             ) if pos_rate > 0 else 0.08
        else:
            # For realistic home run rates, use slightly higher base score
            # Boost the base score by 20%
            base_score = min(0.99, pos_rate * 1.2)

        print(
            f"Using base_score: {base_score:.3f} (positive class rate: {pos_rate:.3f})"
        )

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "n_estimators": [100, 200, 300],
                "learning_rate": [0.01, 0.1, 0.2],
                "max_depth": [3, 5, 7],
                "min_child_weight": [1, 3, 5],
                "subsample": [0.8, 0.9, 1.0],
                "colsample_bytree": [0.8, 0.9, 1.0],
            }

            xgb_model = xgb.XGBClassifier(
                random_state=42,
                eval_metric="logloss",
                objective="binary:logistic",
                use_label_encoder=False,
                base_score=base_score,
            )

            random_search = RandomizedSearchCV(
                xgb_model,
                param_grid,
                n_iter=50,
                cv=5,
                scoring="roc_auc",
                n_jobs=-1,
                random_state=42,
            )
            random_search.fit(X_train, y_train)

            best_model = random_search.best_estimator_
            self.best_params["xgboost"] = random_search.best_params_
        else:
            # Default parameters
            best_model = xgb.XGBClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                min_child_weight=3,
                subsample=0.9,
                colsample_bytree=0.9,
                random_state=42,
                eval_metric="logloss",
                objective="binary:logistic",
                use_label_encoder=False,
                base_score=base_score,
            )
            best_model.fit(X_train, y_train)

        self.trained_models["xgboost"] = best_model
        return best_model

    def train_lightgbm(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> lgb.LGBMClassifier:
        """Train LightGBM model"""

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "n_estimators": [100, 200, 300],
                "learning_rate": [0.01, 0.1, 0.2],
                "max_depth": [3, 5, 7, -1],
                "min_child_samples": [10, 20, 30],
                "subsample": [0.8, 0.9, 1.0],
                "colsample_bytree": [0.8, 0.9, 1.0],
                "reg_alpha": [0, 0.1, 0.5],
                "reg_lambda": [0, 0.1, 0.5],
            }

            lgb_model = lgb.LGBMClassifier(random_state=42, verbose=-1)

            random_search = RandomizedSearchCV(
                lgb_model,
                param_grid,
                n_iter=50,
                cv=5,
                scoring="roc_auc",
                n_jobs=-1,
                random_state=42,
            )
            random_search.fit(X_train, y_train)

            best_model = random_search.best_estimator_
            self.best_params["lightgbm"] = random_search.best_params_
        else:
            # Default parameters
            best_model = lgb.LGBMClassifier(
                n_estimators=200,
                learning_rate=0.1,
                max_depth=5,
                min_child_samples=20,
                subsample=0.9,
                colsample_bytree=0.9,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                verbose=-1,
            )
            best_model.fit(X_train, y_train)

        self.trained_models["lightgbm"] = best_model
        return best_model

    def train_svm(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        tune_hyperparameters: bool = True,
    ) -> SVC:
        """Train Support Vector Machine model"""

        if tune_hyperparameters:
            # Hyperparameter tuning
            param_grid = {
                "C": [0.1, 1, 10, 100],
                "kernel": ["rbf", "poly", "sigmoid"],
                "gamma": ["scale", "auto", 0.001, 0.01, 0.1, 1],
            }

            svm_model = SVC(probability=True, random_state=42)

            grid_search = GridSearchCV(
                svm_model, param_grid, cv=5, scoring="roc_auc", n_jobs=-1
            )
            grid_search.fit(X_train, y_train)

            best_model = grid_search.best_estimator_
            self.best_params["svm"] = grid_search.best_params_
        else:
            # Default parameters
            best_model = SVC(
                C=1.0, kernel="rbf", gamma="scale", probability=True, random_state=42
            )
            best_model.fit(X_train, y_train)

        self.trained_models["svm"] = best_model
        return best_model

    def train_all_models(
        self,
        X_train: pd.DataFrame,
        y_train: pd.Series,
        X_val: pd.DataFrame = None,
        y_val: pd.Series = None,
        models_to_train: List[str] = None,
        tune_hyperparameters: bool = True,
    ) -> Dict[str, Any]:
        """Train multiple models"""

        if models_to_train is None:
            models_to_train = [
                "logistic_regression",
                "random_forest",
                "xgboost",
                "lightgbm",
            ]

        trained_models = {}

        for model_name in models_to_train:
            print(f"Training {model_name}...")

            try:
                if model_name == "logistic_regression":
                    model = self.train_logistic_regression(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                elif model_name == "random_forest":
                    model = self.train_random_forest(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                elif model_name == "gradient_boosting":
                    model = self.train_gradient_boosting(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                elif model_name == "xgboost":
                    model = self.train_xgboost(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                elif model_name == "lightgbm":
                    model = self.train_lightgbm(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                elif model_name == "svm":
                    model = self.train_svm(
                        X_train, y_train, X_val, y_val, tune_hyperparameters
                    )
                else:
                    print(f"Unknown model: {model_name}")
                    continue

                trained_models[model_name] = model
                print(f"✓ {model_name} trained successfully")

            except Exception as e:
                print(f"✗ Error training {model_name}: {str(e)}")
                continue

        return trained_models

    def get_best_hyperparameters(self) -> Dict[str, Dict]:
        """Get best hyperparameters for all trained models"""
        return self.best_params

    def get_trained_models(self) -> Dict[str, Any]:
        """Get all trained models"""
        return self.trained_models
