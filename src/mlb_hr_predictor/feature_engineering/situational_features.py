"""
Situational feature engineering module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional


class SituationalFeatureEngineer:
    """Creates features related to game situation and context"""

    def __init__(self):
        pass

    def create_game_state_features(self, game_state: Dict) -> Dict:
        """Create features based on current game state"""
        features = {}

        # Inning information
        inning = game_state.get("inning", 5)
        features["inning"] = inning
        features["early_innings"] = 1 if inning <= 3 else 0
        features["middle_innings"] = 1 if 4 <= inning <= 6 else 0
        features["late_innings"] = 1 if inning >= 7 else 0
        features["extra_innings"] = 1 if inning > 9 else 0

        # Score situation
        score_diff = game_state.get("score_diff", 0)  # Positive = batter's team ahead
        features["score_diff"] = score_diff
        features["score_diff_abs"] = abs(score_diff)
        features["tied_game"] = 1 if score_diff == 0 else 0
        features["close_game"] = 1 if abs(score_diff) <= 2 else 0
        features["blowout"] = 1 if abs(score_diff) >= 5 else 0
        features["trailing"] = 1 if score_diff < 0 else 0
        features["leading"] = 1 if score_diff > 0 else 0

        # Outs
        outs = game_state.get("outs", 1)
        features["outs"] = outs
        features["no_outs"] = 1 if outs == 0 else 0
        features["one_out"] = 1 if outs == 1 else 0
        features["two_outs"] = 1 if outs == 2 else 0

        return features

    def create_baserunner_features(self, baserunners: Dict) -> Dict:
        """Create features based on baserunner situation"""
        features = {}

        # Individual bases
        features["runner_on_1st"] = 1 if baserunners.get("first", False) else 0
        features["runner_on_2nd"] = 1 if baserunners.get("second", False) else 0
        features["runner_on_3rd"] = 1 if baserunners.get("third", False) else 0

        # Base combinations
        features["bases_empty"] = 1 if not any(baserunners.values()) else 0
        features["runner_in_scoring_position"] = (
            1 if (features["runner_on_2nd"] or features["runner_on_3rd"]) else 0
        )
        features["bases_loaded"] = 1 if all(baserunners.values()) else 0

        # Total runners
        features["total_runners"] = sum(
            [
                features["runner_on_1st"],
                features["runner_on_2nd"],
                features["runner_on_3rd"],
            ]
        )

        # Specific situations
        features["risp_2_outs"] = (
            1
            if (features["runner_in_scoring_position"] and features.get("two_outs", 0))
            else 0
        )
        features["runner_on_3rd_less_than_2_outs"] = (
            1 if (features["runner_on_3rd"] and not features.get("two_outs", 0)) else 0
        )

        return features

    def create_count_features(self, count: Dict) -> Dict:
        """Create features based on pitch count"""
        features = {}

        balls = count.get("balls", 1)
        strikes = count.get("strikes", 1)

        features["balls"] = balls
        features["strikes"] = strikes
        features["count_total"] = balls + strikes

        # Specific counts
        features["count_0_0"] = 1 if balls == 0 and strikes == 0 else 0
        features["count_1_0"] = 1 if balls == 1 and strikes == 0 else 0
        features["count_2_0"] = 1 if balls == 2 and strikes == 0 else 0
        features["count_3_0"] = 1 if balls == 3 and strikes == 0 else 0
        features["count_0_1"] = 1 if balls == 0 and strikes == 1 else 0
        features["count_1_1"] = 1 if balls == 1 and strikes == 1 else 0
        features["count_2_1"] = 1 if balls == 2 and strikes == 1 else 0
        features["count_3_1"] = 1 if balls == 3 and strikes == 1 else 0
        features["count_0_2"] = 1 if balls == 0 and strikes == 2 else 0
        features["count_1_2"] = 1 if balls == 1 and strikes == 2 else 0
        features["count_2_2"] = 1 if balls == 2 and strikes == 2 else 0
        features["count_3_2"] = 1 if balls == 3 and strikes == 2 else 0

        # Count categories
        features["hitters_count"] = 1 if balls > strikes else 0
        features["pitchers_count"] = 1 if strikes > balls else 0
        features["even_count"] = 1 if balls == strikes else 0
        features["full_count"] = 1 if balls == 3 and strikes == 2 else 0
        features["two_strike_count"] = 1 if strikes == 2 else 0
        features["three_ball_count"] = 1 if balls == 3 else 0

        return features

    def create_lineup_features(self, lineup_info: Dict) -> Dict:
        """Create features based on lineup position and batting order"""
        features = {}

        # Batting order position
        position = lineup_info.get("position", 5)
        features["lineup_position"] = position

        # Position categories
        features["leadoff_hitter"] = 1 if position == 1 else 0
        features["top_of_order"] = 1 if position <= 3 else 0
        features["middle_of_order"] = 1 if 4 <= position <= 6 else 0
        features["bottom_of_order"] = 1 if position >= 7 else 0
        features["cleanup_hitter"] = 1 if position == 4 else 0

        # Times through order
        times_through = lineup_info.get("times_through_order", 1)
        features["times_through_order"] = times_through
        features["first_time_through"] = 1 if times_through == 1 else 0
        features["second_time_through"] = 1 if times_through == 2 else 0
        features["third_time_plus"] = 1 if times_through >= 3 else 0

        return features

    def create_pressure_features(
        self, game_state: Dict, baserunners: Dict, count: Dict
    ) -> Dict:
        """Create features that measure pressure/leverage of the situation"""
        features = {}

        # Basic leverage components
        inning = game_state.get("inning", 5)
        score_diff = abs(game_state.get("score_diff", 0))
        outs = game_state.get("outs", 1)
        runners = sum(
            [
                baserunners.get("first", 0),
                baserunners.get("second", 0),
                baserunners.get("third", 0),
            ]
        )

        # Late and close situation
        features["late_and_close"] = 1 if (inning >= 7 and score_diff <= 3) else 0

        # High leverage situation (simplified)
        leverage_score = 0
        if inning >= 7:
            leverage_score += 2
        if score_diff <= 2:
            leverage_score += 2
        if runners >= 2:
            leverage_score += 1
        if outs == 2:
            leverage_score += 1

        features["leverage_score"] = leverage_score
        features["high_leverage"] = 1 if leverage_score >= 4 else 0
        features["medium_leverage"] = 1 if 2 <= leverage_score < 4 else 0
        features["low_leverage"] = 1 if leverage_score < 2 else 0

        # Clutch situation
        features["clutch_situation"] = (
            1
            if (
                (baserunners.get("second", False) or baserunners.get("third", False))
                and inning >= 7
                and score_diff <= 2
            )
            else 0
        )

        # Walk-off potential
        features["walkoff_potential"] = (
            1
            if (
                inning >= 9
                and game_state.get("score_diff", 0) <= 0
                and game_state.get("is_bottom_inning", False)
            )
            else 0
        )

        return features

    def create_rest_fatigue_features(self, player_info: Dict) -> Dict:
        """Create features related to player rest and fatigue"""
        features = {}

        # Days of rest
        rest_days = player_info.get("rest_days", 1)
        features["rest_days"] = rest_days
        features["well_rested"] = 1 if rest_days >= 2 else 0
        features["no_rest"] = 1 if rest_days == 0 else 0
        features["one_day_rest"] = 1 if rest_days == 1 else 0

        # Games played recently
        games_last_7 = player_info.get("games_last_7", 5)
        features["games_last_7"] = games_last_7
        features["heavy_usage"] = 1 if games_last_7 >= 6 else 0
        features["light_usage"] = 1 if games_last_7 <= 3 else 0

        # Season fatigue
        games_played = player_info.get("games_played", 80)
        features["games_played"] = games_played
        features["season_fatigue"] = 1 if games_played >= 140 else 0

        return features

    def combine_all_situational_features(
        self,
        game_state: Dict,
        baserunners: Dict,
        count: Dict,
        lineup_info: Dict,
        player_info: Dict,
    ) -> Dict:
        """Combine all situational features"""
        all_features = {}

        all_features.update(self.create_game_state_features(game_state))
        all_features.update(self.create_baserunner_features(baserunners))
        all_features.update(self.create_count_features(count))
        all_features.update(self.create_lineup_features(lineup_info))
        all_features.update(
            self.create_pressure_features(game_state, baserunners, count)
        )
        all_features.update(self.create_rest_fatigue_features(player_info))

        return all_features
