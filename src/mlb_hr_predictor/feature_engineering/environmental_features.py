"""
Environmental feature engineering module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional


class EnvironmentalFeatureEngineer:
    """Creates features related to ballpark and weather conditions"""

    def __init__(self):
        self.ballpark_data = self._load_ballpark_data()

    def _load_ballpark_data(self) -> Dict:
        """Load ballpark characteristics data"""
        return {
            "Coors Field": {
                "elevation": 5200,
                "hr_factor": 1.50,
                "dimensions": {"lf": 347, "cf": 415, "rf": 350},
            },
            "Yankee Stadium": {
                "elevation": 55,
                "hr_factor": 1.20,
                "dimensions": {"lf": 318, "cf": 408, "rf": 314},
            },
            "Citizens Bank Park": {
                "elevation": 20,
                "hr_factor": 1.10,
                "dimensions": {"lf": 329, "cf": 401, "rf": 330},
            },
            "Wrigley Field": {
                "elevation": 600,
                "hr_factor": 1.10,
                "dimensions": {"lf": 355, "cf": 400, "rf": 353},
            },
            "Fenway Park": {
                "elevation": 21,
                "hr_factor": 1.05,
                "dimensions": {"lf": 310, "cf": 420, "rf": 302},
            },
            "Great American Ball Park": {
                "elevation": 550,
                "hr_factor": 1.05,
                "dimensions": {"lf": 328, "cf": 404, "rf": 325},
            },
            "Oriole Park at Camden Yards": {
                "elevation": 20,
                "hr_factor": 1.05,
                "dimensions": {"lf": 333, "cf": 400, "rf": 318},
            },
            "Rogers Centre": {
                "elevation": 300,
                "hr_factor": 1.05,
                "dimensions": {"lf": 328, "cf": 400, "rf": 328},
            },
            "Guaranteed Rate Field": {
                "elevation": 595,
                "hr_factor": 1.05,
                "dimensions": {"lf": 330, "cf": 400, "rf": 335},
            },
            "Truist Park": {
                "elevation": 1050,
                "hr_factor": 1.00,
                "dimensions": {"lf": 335, "cf": 400, "rf": 325},
            },
            "Globe Life Field": {
                "elevation": 551,
                "hr_factor": 1.00,
                "dimensions": {"lf": 329, "cf": 407, "rf": 326},
            },
            "Target Field": {
                "elevation": 815,
                "hr_factor": 1.00,
                "dimensions": {"lf": 339, "cf": 411, "rf": 328},
            },
            "Nationals Park": {
                "elevation": 15,
                "hr_factor": 1.00,
                "dimensions": {"lf": 336, "cf": 402, "rf": 335},
            },
            "American Family Field": {
                "elevation": 635,
                "hr_factor": 0.95,
                "dimensions": {"lf": 344, "cf": 400, "rf": 345},
            },
            "PNC Park": {
                "elevation": 730,
                "hr_factor": 0.95,
                "dimensions": {"lf": 325, "cf": 399, "rf": 320},
            },
            "Angel Stadium": {
                "elevation": 160,
                "hr_factor": 0.95,
                "dimensions": {"lf": 330, "cf": 400, "rf": 330},
            },
            "Progressive Field": {
                "elevation": 660,
                "hr_factor": 0.95,
                "dimensions": {"lf": 325, "cf": 405, "rf": 325},
            },
            "loanDepot park": {
                "elevation": 10,
                "hr_factor": 0.95,
                "dimensions": {"lf": 344, "cf": 407, "rf": 335},
            },
            "Busch Stadium": {
                "elevation": 465,
                "hr_factor": 0.90,
                "dimensions": {"lf": 336, "cf": 400, "rf": 335},
            },
            "Minute Maid Park": {
                "elevation": 22,
                "hr_factor": 0.90,
                "dimensions": {"lf": 315, "cf": 436, "rf": 326},
            },
            "T-Mobile Park": {
                "elevation": 15,
                "hr_factor": 0.90,
                "dimensions": {"lf": 331, "cf": 401, "rf": 326},
            },
            "Citi Field": {
                "elevation": 20,
                "hr_factor": 0.90,
                "dimensions": {"lf": 335, "cf": 408, "rf": 330},
            },
            "Kauffman Stadium": {
                "elevation": 750,
                "hr_factor": 0.90,
                "dimensions": {"lf": 330, "cf": 410, "rf": 330},
            },
            "Tropicana Field": {
                "elevation": 15,
                "hr_factor": 0.90,
                "dimensions": {"lf": 315, "cf": 404, "rf": 322},
            },
            "Oakland Coliseum": {
                "elevation": 6,
                "hr_factor": 0.85,
                "dimensions": {"lf": 330, "cf": 400, "rf": 330},
            },
            "Dodger Stadium": {
                "elevation": 340,
                "hr_factor": 0.85,
                "dimensions": {"lf": 330, "cf": 395, "rf": 330},
            },
            "Petco Park": {
                "elevation": 62,
                "hr_factor": 0.85,
                "dimensions": {"lf": 336, "cf": 396, "rf": 322},
            },
            "Comerica Park": {
                "elevation": 585,
                "hr_factor": 0.85,
                "dimensions": {"lf": 345, "cf": 420, "rf": 330},
            },
            "Oracle Park": {
                "elevation": 12,
                "hr_factor": 0.75,
                "dimensions": {"lf": 339, "cf": 399, "rf": 309},
            },
        }

    def create_ballpark_features(self, ballpark_name: str) -> Dict:
        """Create features based on ballpark characteristics"""
        features = {}

        if ballpark_name in self.ballpark_data:
            park_data = self.ballpark_data[ballpark_name]

            # Basic ballpark factors
            features["ballpark_hr_factor"] = park_data["hr_factor"]
            features["ballpark_elevation"] = park_data["elevation"]

            # Dimension features
            dimensions = park_data["dimensions"]
            features["ballpark_lf_distance"] = dimensions["lf"]
            features["ballpark_cf_distance"] = dimensions["cf"]
            features["ballpark_rf_distance"] = dimensions["rf"]

            # Derived dimension features
            features["ballpark_avg_distance"] = (
                dimensions["lf"] + dimensions["cf"] + dimensions["rf"]
            ) / 3
            features["ballpark_foul_territory"] = self._estimate_foul_territory(
                ballpark_name
            )

            # Categorical features
            features["is_hitter_friendly"] = 1 if park_data["hr_factor"] > 1.05 else 0
            features["is_pitcher_friendly"] = 1 if park_data["hr_factor"] < 0.95 else 0
            features["is_neutral_park"] = (
                1 if 0.95 <= park_data["hr_factor"] <= 1.05 else 0
            )

            # Elevation categories
            features["high_elevation"] = 1 if park_data["elevation"] > 1000 else 0
            features["very_high_elevation"] = 1 if park_data["elevation"] > 3000 else 0

        else:
            # Default values for unknown ballparks
            features.update(self._default_ballpark_features())

        return features

    def create_weather_features(self, weather_data: Dict) -> Dict:
        """Create features based on weather conditions"""
        features = {}

        # Temperature effects
        temp = weather_data.get("temperature", 75)
        features["temperature"] = temp
        features["temperature_squared"] = temp**2  # Non-linear temperature effects
        features["hot_weather"] = 1 if temp > 80 else 0
        features["cold_weather"] = 1 if temp < 60 else 0
        features["ideal_temp"] = 1 if 70 <= temp <= 85 else 0

        # Temperature impact on ball flight (warmer = more HRs)
        features["temp_hr_factor"] = (
            1 + (temp - 70) * 0.005
        )  # 0.5% per degree above 70F

        # Humidity effects
        humidity = weather_data.get("humidity", 60)
        features["humidity"] = humidity
        features["high_humidity"] = 1 if humidity > 70 else 0
        features["low_humidity"] = 1 if humidity < 40 else 0

        # Wind effects
        wind_speed = weather_data.get("wind_speed", 5)
        wind_direction = weather_data.get("wind_direction", "calm")

        features["wind_speed"] = wind_speed
        features["strong_wind"] = 1 if wind_speed > 15 else 0
        features["calm_wind"] = 1 if wind_speed < 5 else 0

        # Wind direction impact
        features["wind_out_to_rf"] = 1 if "out_to_rf" in wind_direction else 0
        features["wind_out_to_lf"] = 1 if "out_to_lf" in wind_direction else 0
        features["wind_out_to_cf"] = 1 if "out_to_cf" in wind_direction else 0
        features["wind_in_from_rf"] = 1 if "in_from_rf" in wind_direction else 0
        features["wind_in_from_lf"] = 1 if "in_from_lf" in wind_direction else 0
        features["wind_in_from_cf"] = 1 if "in_from_cf" in wind_direction else 0

        # Combined wind effect
        if (
            features["wind_out_to_rf"]
            or features["wind_out_to_lf"]
            or features["wind_out_to_cf"]
        ):
            features["wind_helping_hrs"] = (
                wind_speed / 10
            )  # Stronger helping wind = more impact
        elif (
            features["wind_in_from_rf"]
            or features["wind_in_from_lf"]
            or features["wind_in_from_cf"]
        ):
            features["wind_helping_hrs"] = -wind_speed / 10  # Hindering wind
        else:
            features["wind_helping_hrs"] = 0

        # Atmospheric pressure
        pressure = weather_data.get("pressure", 30.0)
        features["pressure"] = pressure
        features["low_pressure"] = (
            1 if pressure < 29.8 else 0
        )  # Low pressure = ball carries more
        features["high_pressure"] = 1 if pressure > 30.2 else 0

        return features

    def create_time_features(
        self, game_datetime: str, is_day_game: bool = True
    ) -> Dict:
        """Create features based on time of game"""
        features = {}

        # Day vs night
        features["is_day_game"] = 1 if is_day_game else 0
        features["is_night_game"] = 1 - features["is_day_game"]

        # Parse datetime if provided
        if game_datetime:
            try:
                dt = pd.to_datetime(game_datetime)

                # Month effects (season progression)
                features["month"] = dt.month
                features["early_season"] = 1 if dt.month in [3, 4] else 0
                features["mid_season"] = 1 if dt.month in [5, 6, 7, 8] else 0
                features["late_season"] = 1 if dt.month in [9, 10] else 0

                # Day of week
                features["day_of_week"] = dt.dayofweek
                features["is_weekend"] = 1 if dt.dayofweek in [5, 6] else 0

            except:
                # Default values if datetime parsing fails
                features.update(self._default_time_features())
        else:
            features.update(self._default_time_features())

        return features

    def _estimate_foul_territory(self, ballpark_name: str) -> float:
        """Estimate foul territory size (larger = fewer HRs)"""
        large_foul_territory = [
            "Oakland Coliseum",
            "Tropicana Field",
            "Kauffman Stadium",
        ]
        small_foul_territory = ["Fenway Park", "Yankee Stadium", "Minute Maid Park"]

        if ballpark_name in large_foul_territory:
            return 1.2
        elif ballpark_name in small_foul_territory:
            return 0.8
        else:
            return 1.0

    def _default_ballpark_features(self) -> Dict:
        """Default ballpark features for unknown parks"""
        return {
            "ballpark_hr_factor": 1.0,
            "ballpark_elevation": 500,
            "ballpark_lf_distance": 330,
            "ballpark_cf_distance": 400,
            "ballpark_rf_distance": 330,
            "ballpark_avg_distance": 353,
            "ballpark_foul_territory": 1.0,
            "is_hitter_friendly": 0,
            "is_pitcher_friendly": 0,
            "is_neutral_park": 1,
            "high_elevation": 0,
            "very_high_elevation": 0,
        }

    def _default_time_features(self) -> Dict:
        """Default time features"""
        return {
            "month": 6,
            "early_season": 0,
            "mid_season": 1,
            "late_season": 0,
            "day_of_week": 2,
            "is_weekend": 0,
        }

    def combine_all_environmental_features(
        self,
        ballpark_name: str,
        weather_data: Dict,
        game_datetime: str,
        is_day_game: bool = True,
    ) -> Dict:
        """Combine all environmental features"""
        all_features = {}

        all_features.update(self.create_ballpark_features(ballpark_name))
        all_features.update(self.create_weather_features(weather_data))
        all_features.update(self.create_time_features(game_datetime, is_day_game))

        return all_features
