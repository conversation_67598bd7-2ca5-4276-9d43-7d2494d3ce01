"""
Player feature engineering module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional
from datetime import datetime, timedelta


class PlayerFeatureEngineer:
    """Creates features related to batter performance and characteristics"""

    def __init__(self):
        self.feature_cache = {}

    def create_career_features(self, player_stats: pd.DataFrame) -> Dict:
        """Create career-based features for a player"""
        if player_stats.empty:
            return {}

        # Sort by season if Season column exists
        if "Season" in player_stats.columns:
            stats = player_stats.sort_values("Season")
        else:
            # If no Season column, assume data is already in chronological order
            stats = player_stats.copy()

        features = {}

        # Career totals and rates
        features["career_hr"] = stats["HR"].sum()
        features["career_ab"] = stats["AB"].sum()
        features["career_pa"] = stats["PA"].sum()
        features["career_hr_rate"] = features["career_hr"] / max(
            features["career_ab"], 1
        )
        features["career_hr_per_pa"] = features["career_hr"] / max(
            features["career_pa"], 1
        )

        # Career averages (with safe defaults)
        features["career_avg"] = (
            stats["AVG"].mean() if "AVG" in stats.columns else 0.250
        )
        features["career_slg"] = (
            stats["SLG"].mean() if "SLG" in stats.columns else 0.400
        )
        features["career_ops"] = (
            stats["OPS"].mean() if "OPS" in stats.columns else 0.700
        )

        # Calculate ISO safely
        if "SLG" in stats.columns and "AVG" in stats.columns:
            features["career_iso"] = (stats["SLG"] - stats["AVG"]).mean()
        else:
            features["career_iso"] = 0.150

        # Career consistency (with safe defaults)
        if "HR" in stats.columns and len(stats) > 1:
            features["hr_consistency"] = 1 / (stats["HR"].std() + 1)
        else:
            features["hr_consistency"] = 0.5

        if "SLG" in stats.columns and len(stats) > 1:
            features["slg_consistency"] = 1 / (stats["SLG"].std() + 0.1)
        else:
            features["slg_consistency"] = 0.5

        # Career trajectory (with safe defaults)
        if len(stats) >= 3 and "HR" in stats.columns:
            recent_seasons = stats.tail(3)
            early_seasons = stats.head(3)
            features["hr_trend"] = (
                recent_seasons["HR"].mean() - early_seasons["HR"].mean()
            )
        else:
            features["hr_trend"] = 0.0

        if len(stats) >= 3 and "SLG" in stats.columns:
            recent_seasons = stats.tail(3)
            early_seasons = stats.head(3)
            features["slg_trend"] = (
                recent_seasons["SLG"].mean() - early_seasons["SLG"].mean()
            )
        else:
            features["slg_trend"] = 0.0

        # Peak performance (with safe defaults)
        features["peak_hr_season"] = stats["HR"].max() if "HR" in stats.columns else 0
        features["peak_slg_season"] = (
            stats["SLG"].max() if "SLG" in stats.columns else 0.400
        )

        return features

    def create_recent_form_features(
        self, recent_games: pd.DataFrame, window: int = 30
    ) -> Dict:
        """Create features based on recent performance"""
        if recent_games.empty:
            return {}

        features = {}

        # Recent performance metrics
        features["recent_hr"] = recent_games["HR"].sum()
        features["recent_ab"] = recent_games["AB"].sum()
        features["recent_pa"] = recent_games["PA"].sum()
        features["recent_hr_rate"] = features["recent_hr"] / max(
            features["recent_ab"], 1
        )

        # Recent averages (with safe defaults)
        features["recent_avg"] = (
            recent_games["AVG"].mean() if "AVG" in recent_games.columns else 0.250
        )
        features["recent_slg"] = (
            recent_games["SLG"].mean() if "SLG" in recent_games.columns else 0.400
        )
        features["recent_ops"] = (
            recent_games["OPS"].mean() if "OPS" in recent_games.columns else 0.700
        )

        # Hot/cold streak indicators
        features["games_since_last_hr"] = self._calculate_games_since_last_hr(
            recent_games
        )
        features["is_hot_streak"] = self._detect_hot_streak(recent_games)
        features["consecutive_games_with_hit"] = self._consecutive_games_with_hit(
            recent_games
        )

        # Form score (composite metric)
        features["form_score"] = self._calculate_form_score(recent_games)

        return features

    def create_situational_features(self, splits_data: Dict) -> Dict:
        """Create features based on situational performance"""
        features = {}

        # Handedness splits
        if "vs_lhp" in splits_data and "vs_rhp" in splits_data:
            lhp_hr_rate = splits_data["vs_lhp"]["HR"] / max(
                splits_data["vs_lhp"]["AB"], 1
            )
            rhp_hr_rate = splits_data["vs_rhp"]["HR"] / max(
                splits_data["vs_rhp"]["AB"], 1
            )

            features["vs_lhp_hr_rate"] = lhp_hr_rate
            features["vs_rhp_hr_rate"] = rhp_hr_rate
            features["handedness_split_diff"] = lhp_hr_rate - rhp_hr_rate

        # Home/away splits
        if "home" in splits_data and "away" in splits_data:
            home_hr_rate = splits_data["home"]["HR"] / max(splits_data["home"]["AB"], 1)
            away_hr_rate = splits_data["away"]["HR"] / max(splits_data["away"]["AB"], 1)

            features["home_hr_rate"] = home_hr_rate
            features["away_hr_rate"] = away_hr_rate
            features["home_away_split_diff"] = home_hr_rate - away_hr_rate

        # Clutch performance
        if "clutch" in splits_data:
            features["clutch_hr_rate"] = splits_data["clutch"]["HR"] / max(
                splits_data["clutch"]["AB"], 1
            )

        return features

    def create_physical_features(self, player_info: Dict) -> Dict:
        """Create features based on player physical characteristics"""
        features = {}

        if "birth_date" in player_info:
            # Calculate age
            birth_date = pd.to_datetime(player_info["birth_date"])
            age = (datetime.now() - birth_date).days / 365.25
            features["age"] = age
            features["age_squared"] = age**2  # For non-linear age effects

            # Career stage
            if age < 25:
                features["career_stage"] = "young"
            elif age < 30:
                features["career_stage"] = "prime"
            elif age < 35:
                features["career_stage"] = "veteran"
            else:
                features["career_stage"] = "old"

        # Physical attributes (if available)
        if "height" in player_info:
            features["height_inches"] = player_info["height"]

        if "weight" in player_info:
            features["weight_lbs"] = player_info["weight"]

        if "bats" in player_info:
            features["bats_left"] = 1 if player_info["bats"] == "L" else 0
            features["bats_right"] = 1 if player_info["bats"] == "R" else 0
            features["bats_switch"] = 1 if player_info["bats"] == "S" else 0

        return features

    def create_advanced_features(self, statcast_metrics: Dict) -> Dict:
        """Create features from advanced Statcast metrics"""
        features = {}

        # Exit velocity features
        if "avg_exit_velocity" in statcast_metrics:
            features["avg_exit_velocity"] = statcast_metrics["avg_exit_velocity"]
            features["max_exit_velocity"] = statcast_metrics.get("max_exit_velocity", 0)
            features["hard_hit_rate"] = statcast_metrics.get("hard_hit_rate", 0)

        # Launch angle features
        if "avg_launch_angle" in statcast_metrics:
            features["avg_launch_angle"] = statcast_metrics["avg_launch_angle"]
            features["sweet_spot_rate"] = statcast_metrics.get("sweet_spot_rate", 0)

        # Quality of contact
        features["barrel_rate"] = statcast_metrics.get("barrel_rate", 0)
        features["xwoba"] = statcast_metrics.get("xwoba", 0)
        features["xba"] = statcast_metrics.get("xba", 0)

        # Speed
        features["sprint_speed"] = statcast_metrics.get("sprint_speed", 0)

        return features

    def _calculate_games_since_last_hr(self, recent_games: pd.DataFrame) -> int:
        """Calculate games since last home run"""
        if recent_games.empty:
            return 999  # Large number if no data

        # Find most recent game with HR
        hr_games = recent_games[recent_games["HR"] > 0]
        if hr_games.empty:
            return len(recent_games)

        last_hr_index = hr_games.index[-1]
        return len(recent_games) - recent_games.index.get_loc(last_hr_index) - 1

    def _detect_hot_streak(
        self, recent_games: pd.DataFrame, threshold: int = 5
    ) -> bool:
        """Detect if player is on a hot streak"""
        if len(recent_games) < threshold:
            return False

        recent_window = recent_games.tail(threshold)
        avg_ops = recent_window["OPS"].mean()
        season_ops = recent_games["OPS"].mean()

        return avg_ops > season_ops * 1.2  # 20% above season average

    def _consecutive_games_with_hit(self, recent_games: pd.DataFrame) -> int:
        """Calculate consecutive games with a hit"""
        if recent_games.empty:
            return 0

        consecutive = 0
        for _, game in recent_games.iterrows():
            if game.get("H", 0) > 0:
                consecutive += 1
            else:
                break

        return consecutive

    def _calculate_form_score(self, recent_games: pd.DataFrame) -> float:
        """Calculate composite form score"""
        if recent_games.empty:
            return 0.0

        # Weight recent games more heavily
        weights = np.exp(np.linspace(-1, 0, len(recent_games)))
        weights = weights / weights.sum()

        # Combine multiple metrics
        ops_score = (recent_games["OPS"] * weights).sum()
        hr_score = (recent_games["HR"] * weights).sum()

        return ops_score + hr_score * 2  # Weight HRs more heavily

    def combine_all_player_features(
        self,
        player_stats: pd.DataFrame,
        recent_games: pd.DataFrame,
        splits_data: Dict,
        player_info: Dict,
        statcast_metrics: Dict,
    ) -> Dict:
        """Combine all player features into a single dictionary"""
        all_features = {}

        # Combine all feature types
        all_features.update(self.create_career_features(player_stats))
        all_features.update(self.create_recent_form_features(recent_games))
        all_features.update(self.create_situational_features(splits_data))
        all_features.update(self.create_physical_features(player_info))
        all_features.update(self.create_advanced_features(statcast_metrics))

        return all_features
