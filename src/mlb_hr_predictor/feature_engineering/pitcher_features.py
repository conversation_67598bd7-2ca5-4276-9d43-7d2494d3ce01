"""
Pitcher feature engineering module
"""

import pandas as pd
import numpy as np
from typing import Dict, List, Optional


class PitcherFeatureEngineer:
    """Creates features related to pitcher performance and matchup factors"""

    def __init__(self):
        self.feature_cache = {}

    def create_pitcher_performance_features(self, pitcher_stats: pd.DataFrame) -> Dict:
        """Create features based on pitcher's overall performance"""
        if pitcher_stats.empty:
            return {}

        features = {}

        # Basic rate stats
        features["pitcher_era"] = (
            pitcher_stats["ERA"].iloc[-1] if "ERA" in pitcher_stats.columns else 4.50
        )
        features["pitcher_whip"] = (
            pitcher_stats["WHIP"].iloc[-1] if "WHIP" in pitcher_stats.columns else 1.30
        )
        features["pitcher_hr_per_9"] = (
            pitcher_stats.get("HR/9", 1.0).iloc[-1]
            if "HR/9" in pitcher_stats.columns
            else 1.0
        )

        # Strikeout and walk rates
        features["pitcher_k_per_9"] = (
            pitcher_stats.get("K/9", 8.0).iloc[-1]
            if "K/9" in pitcher_stats.columns
            else 8.0
        )
        features["pitcher_bb_per_9"] = (
            pitcher_stats.get("BB/9", 3.0).iloc[-1]
            if "BB/9" in pitcher_stats.columns
            else 3.0
        )
        features["pitcher_k_bb_ratio"] = features["pitcher_k_per_9"] / max(
            features["pitcher_bb_per_9"], 0.1
        )

        # Contact quality metrics
        if "Hard%" in pitcher_stats.columns:
            features["pitcher_hard_contact_allowed"] = (
                pitcher_stats["Hard%"].iloc[-1] / 100
            )

        if "FB%" in pitcher_stats.columns:
            features["pitcher_fb_rate"] = pitcher_stats["FB%"].iloc[-1] / 100

        # Velocity (if available)
        if "vFA (pfx)" in pitcher_stats.columns:
            features["pitcher_avg_velocity"] = pitcher_stats["vFA (pfx)"].iloc[-1]

        return features

    def create_pitcher_recent_form_features(self, recent_stats: Dict) -> Dict:
        """Create features based on pitcher's recent performance"""
        features = {}

        features["pitcher_recent_era"] = recent_stats.get("recent_era", 4.50)
        features["pitcher_recent_whip"] = recent_stats.get("recent_whip", 1.30)
        features["pitcher_recent_hr_per_9"] = recent_stats.get("recent_hr_per_9", 1.0)
        features["pitcher_recent_k_per_9"] = recent_stats.get("recent_k_per_9", 8.0)
        features["pitcher_recent_bb_per_9"] = recent_stats.get("recent_bb_per_9", 3.0)

        return features

    def create_matchup_features(
        self, pitcher_info: Dict, batter_info: Dict, pitcher_splits: Dict
    ) -> Dict:
        """Create features based on pitcher-batter matchup"""
        features = {}

        # Handedness matchup
        pitcher_throws = pitcher_info.get("throws", "R")
        batter_bats = batter_info.get("bats", "R")

        features["same_handedness"] = 1 if pitcher_throws == batter_bats else 0
        features["opposite_handedness"] = 1 - features["same_handedness"]

        # Platoon advantage
        if pitcher_throws == "R" and batter_bats == "L":
            features["platoon_advantage"] = "pitcher"
        elif pitcher_throws == "L" and batter_bats == "R":
            features["platoon_advantage"] = "pitcher"
        else:
            features["platoon_advantage"] = "batter"

        # Convert to numeric
        features["pitcher_platoon_advantage"] = (
            1 if features["platoon_advantage"] == "pitcher" else 0
        )
        features["batter_platoon_advantage"] = (
            1 if features["platoon_advantage"] == "batter" else 0
        )

        # Historical matchup performance (if available)
        if batter_bats == "L" and "vs_lhb" in pitcher_splits:
            features["pitcher_vs_handedness_hr_rate"] = pitcher_splits["vs_lhb"][
                "HR"
            ] / max(pitcher_splits["vs_lhb"]["AB"], 1)
            features["pitcher_vs_handedness_slg"] = pitcher_splits["vs_lhb"]["SLG"]
        elif batter_bats == "R" and "vs_rhb" in pitcher_splits:
            features["pitcher_vs_handedness_hr_rate"] = pitcher_splits["vs_rhb"][
                "HR"
            ] / max(pitcher_splits["vs_rhb"]["AB"], 1)
            features["pitcher_vs_handedness_slg"] = pitcher_splits["vs_rhb"]["SLG"]
        else:
            features["pitcher_vs_handedness_hr_rate"] = 0.03  # League average
            features["pitcher_vs_handedness_slg"] = 0.400

        return features

    def create_pitcher_statcast_features(self, statcast_metrics: Dict) -> Dict:
        """Create features from pitcher Statcast data"""
        features = {}

        # Velocity metrics
        features["pitcher_avg_velocity"] = statcast_metrics.get("avg_velocity", 92.0)
        features["pitcher_max_velocity"] = statcast_metrics.get("max_velocity", 95.0)

        # Contact quality allowed
        features["pitcher_avg_exit_velo_allowed"] = statcast_metrics.get(
            "avg_exit_velo_allowed", 87.0
        )
        features["pitcher_hard_contact_rate_allowed"] = statcast_metrics.get(
            "hard_contact_rate_allowed", 0.35
        )
        features["pitcher_barrel_rate_allowed"] = statcast_metrics.get(
            "barrel_rate_allowed", 0.08
        )

        # Expected stats
        features["pitcher_xwoba_allowed"] = statcast_metrics.get("xwoba_allowed", 0.320)

        return features

    def create_pitcher_ballpark_features(
        self, ballpark_factors: Dict, ballpark_name: str
    ) -> Dict:
        """Create features based on pitcher performance in specific ballparks"""
        features = {}

        # Home/away performance
        features["pitcher_home_hr_rate"] = ballpark_factors.get("home_hr_rate", 1.0)
        features["pitcher_away_hr_rate"] = ballpark_factors.get("away_hr_rate", 1.0)

        # Ballpark-specific factors
        features["pitcher_favorable_ballpark"] = (
            1 if ballpark_name in ballpark_factors.get("favorable_parks", []) else 0
        )
        features["pitcher_unfavorable_ballpark"] = (
            1 if ballpark_name in ballpark_factors.get("unfavorable_parks", []) else 0
        )

        return features

    def create_pitcher_fatigue_features(
        self, pitcher_info: Dict, game_context: Dict
    ) -> Dict:
        """Create features related to pitcher fatigue and usage"""
        features = {}

        # Rest days
        features["pitcher_rest_days"] = game_context.get("rest_days", 4)
        features["pitcher_well_rested"] = 1 if features["pitcher_rest_days"] >= 4 else 0
        features["pitcher_short_rest"] = 1 if features["pitcher_rest_days"] <= 3 else 0

        # Season workload (if available)
        features["pitcher_innings_pitched"] = pitcher_info.get("innings_pitched", 100)
        features["pitcher_high_workload"] = (
            1 if features["pitcher_innings_pitched"] > 180 else 0
        )

        # Game situation
        features["pitcher_pitch_count"] = game_context.get("pitch_count", 50)
        features["pitcher_high_pitch_count"] = (
            1 if features["pitcher_pitch_count"] > 100 else 0
        )

        return features

    def combine_all_pitcher_features(
        self,
        pitcher_stats: pd.DataFrame,
        recent_stats: Dict,
        pitcher_info: Dict,
        batter_info: Dict,
        pitcher_splits: Dict,
        statcast_metrics: Dict,
        ballpark_factors: Dict,
        ballpark_name: str,
        game_context: Dict,
    ) -> Dict:
        """Combine all pitcher features into a single dictionary"""
        all_features = {}

        # Combine all feature types
        all_features.update(self.create_pitcher_performance_features(pitcher_stats))
        all_features.update(self.create_pitcher_recent_form_features(recent_stats))
        all_features.update(
            self.create_matchup_features(pitcher_info, batter_info, pitcher_splits)
        )
        all_features.update(self.create_pitcher_statcast_features(statcast_metrics))
        all_features.update(
            self.create_pitcher_ballpark_features(ballpark_factors, ballpark_name)
        )
        all_features.update(
            self.create_pitcher_fatigue_features(pitcher_info, game_context)
        )

        return all_features
