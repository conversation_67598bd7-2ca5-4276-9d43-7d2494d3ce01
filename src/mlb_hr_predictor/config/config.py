"""
Configuration settings for MLB Home Run Predictor
"""

import os
from pathlib import Path

# Project paths
PROJECT_ROOT = Path(__file__).parent.parent
DATA_DIR = PROJECT_ROOT / "data"
RAW_DATA_DIR = DATA_DIR / "raw"
PROCESSED_DATA_DIR = DATA_DIR / "processed"
EXTERNAL_DATA_DIR = DATA_DIR / "external"
MODELS_DIR = PROJECT_ROOT / "models"
NOTEBOOKS_DIR = PROJECT_ROOT / "notebooks"

# Create directories if they don't exist
for directory in [RAW_DATA_DIR, PROCESSED_DATA_DIR, EXTERNAL_DATA_DIR, MODELS_DIR, NOTEBOOKS_DIR]:
    directory.mkdir(parents=True, exist_ok=True)

# Data collection settings
CURRENT_SEASON = 2025
SEASONS_TO_ANALYZE = [2020, 2021, 2022, 2023, 2024, 2025]
MIN_PLATE_APPEARANCES = 100  # Minimum PA for a player to be included

# Feature engineering settings
RECENT_GAMES_WINDOW = 30  # Number of recent games to consider for form
HOT_STREAK_THRESHOLD = 5  # Number of games to define hot/cold streaks
WEATHER_IMPACT_TEMP_THRESHOLD = 70  # Temperature threshold for weather impact

# Model settings
TEST_SIZE = 0.2
RANDOM_STATE = 42
CV_FOLDS = 5

# API settings (if needed for weather data)
WEATHER_API_KEY = os.getenv("WEATHER_API_KEY")
ODDS_API_KEY = os.getenv("ODDS_API_KEY")

# Stadium information - Complete MLB ballpark data
STADIUM_INFO = {
    # Most hitter-friendly parks
    'Coors Field': {
        'team': 'COL', 'elevation': 5200, 'hr_factor': 1.50,
        'dimensions': {'lf': 347, 'cf': 415, 'rf': 350},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 1995
    },
    'Yankee Stadium': {
        'team': 'NYY', 'elevation': 55, 'hr_factor': 1.20,
        'dimensions': {'lf': 318, 'cf': 408, 'rf': 314},
        'wall_height': {'lf': 6, 'cf': 6, 'rf': 10},
        'foul_territory': 'small', 'opened': 2009
    },
    'Citizens Bank Park': {
        'team': 'PHI', 'elevation': 20, 'hr_factor': 1.10,
        'dimensions': {'lf': 329, 'cf': 401, 'rf': 330},
        'wall_height': {'lf': 6, 'cf': 6, 'rf': 6},
        'foul_territory': 'small', 'opened': 2004
    },
    'Wrigley Field': {
        'team': 'CHC', 'elevation': 600, 'hr_factor': 1.10,
        'dimensions': {'lf': 355, 'cf': 400, 'rf': 353},
        'wall_height': {'lf': 11, 'cf': 11, 'rf': 11},
        'foul_territory': 'small', 'opened': 1914
    },
    'Fenway Park': {
        'team': 'BOS', 'elevation': 21, 'hr_factor': 1.05,
        'dimensions': {'lf': 310, 'cf': 420, 'rf': 302},
        'wall_height': {'lf': 37, 'cf': 17, 'rf': 3},
        'foul_territory': 'small', 'opened': 1912
    },
    'Great American Ball Park': {
        'team': 'CIN', 'elevation': 550, 'hr_factor': 1.05,
        'dimensions': {'lf': 328, 'cf': 404, 'rf': 325},
        'wall_height': {'lf': 12, 'cf': 12, 'rf': 12},
        'foul_territory': 'average', 'opened': 2003
    },
    'Oriole Park at Camden Yards': {
        'team': 'BAL', 'elevation': 20, 'hr_factor': 1.05,
        'dimensions': {'lf': 333, 'cf': 400, 'rf': 318},
        'wall_height': {'lf': 7, 'cf': 7, 'rf': 25},
        'foul_territory': 'small', 'opened': 1992
    },
    'Rogers Centre': {
        'team': 'TOR', 'elevation': 300, 'hr_factor': 1.05,
        'dimensions': {'lf': 328, 'cf': 400, 'rf': 328},
        'wall_height': {'lf': 10, 'cf': 10, 'rf': 10},
        'foul_territory': 'average', 'opened': 1989
    },
    'Guaranteed Rate Field': {
        'team': 'CWS', 'elevation': 595, 'hr_factor': 1.05,
        'dimensions': {'lf': 330, 'cf': 400, 'rf': 335},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 1991
    },

    # Neutral parks
    'Truist Park': {
        'team': 'ATL', 'elevation': 1050, 'hr_factor': 1.00,
        'dimensions': {'lf': 335, 'cf': 400, 'rf': 325},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2017
    },
    'Globe Life Field': {
        'team': 'TEX', 'elevation': 551, 'hr_factor': 1.00,
        'dimensions': {'lf': 329, 'cf': 407, 'rf': 326},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2020
    },
    'Target Field': {
        'team': 'MIN', 'elevation': 815, 'hr_factor': 1.00,
        'dimensions': {'lf': 339, 'cf': 411, 'rf': 328},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2010
    },
    'Nationals Park': {
        'team': 'WSH', 'elevation': 15, 'hr_factor': 1.00,
        'dimensions': {'lf': 336, 'cf': 402, 'rf': 335},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2008
    },

    # Pitcher-friendly parks
    'American Family Field': {
        'team': 'MIL', 'elevation': 635, 'hr_factor': 0.95,
        'dimensions': {'lf': 344, 'cf': 400, 'rf': 345},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2001
    },
    'PNC Park': {
        'team': 'PIT', 'elevation': 730, 'hr_factor': 0.95,
        'dimensions': {'lf': 325, 'cf': 399, 'rf': 320},
        'wall_height': {'lf': 6, 'cf': 6, 'rf': 21},
        'foul_territory': 'average', 'opened': 2001
    },
    'Angel Stadium': {
        'team': 'LAA', 'elevation': 160, 'hr_factor': 0.95,
        'dimensions': {'lf': 330, 'cf': 400, 'rf': 330},
        'wall_height': {'lf': 18, 'cf': 18, 'rf': 18},
        'foul_territory': 'large', 'opened': 1966
    },
    'Progressive Field': {
        'team': 'CLE', 'elevation': 660, 'hr_factor': 0.95,
        'dimensions': {'lf': 325, 'cf': 405, 'rf': 325},
        'wall_height': {'lf': 19, 'cf': 19, 'rf': 19},
        'foul_territory': 'average', 'opened': 1994
    },
    'loanDepot park': {
        'team': 'MIA', 'elevation': 10, 'hr_factor': 0.95,
        'dimensions': {'lf': 344, 'cf': 407, 'rf': 335},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2012
    },
    'Busch Stadium': {
        'team': 'STL', 'elevation': 465, 'hr_factor': 0.90,
        'dimensions': {'lf': 336, 'cf': 400, 'rf': 335},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2006
    },
    'Minute Maid Park': {
        'team': 'HOU', 'elevation': 22, 'hr_factor': 0.90,
        'dimensions': {'lf': 315, 'cf': 436, 'rf': 326},
        'wall_height': {'lf': 19, 'cf': 19, 'rf': 19},
        'foul_territory': 'small', 'opened': 2000
    },
    'T-Mobile Park': {
        'team': 'SEA', 'elevation': 15, 'hr_factor': 0.90,
        'dimensions': {'lf': 331, 'cf': 401, 'rf': 326},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 1999
    },
    'Citi Field': {
        'team': 'NYM', 'elevation': 20, 'hr_factor': 0.90,
        'dimensions': {'lf': 335, 'cf': 408, 'rf': 330},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2009
    },
    'Kauffman Stadium': {
        'team': 'KC', 'elevation': 750, 'hr_factor': 0.90,
        'dimensions': {'lf': 330, 'cf': 410, 'rf': 330},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'large', 'opened': 1973
    },
    'Tropicana Field': {
        'team': 'TB', 'elevation': 15, 'hr_factor': 0.90,
        'dimensions': {'lf': 315, 'cf': 404, 'rf': 322},
        'wall_height': {'lf': 10, 'cf': 10, 'rf': 10},
        'foul_territory': 'large', 'opened': 1990
    },
    'Oakland Coliseum': {
        'team': 'OAK', 'elevation': 6, 'hr_factor': 0.85,
        'dimensions': {'lf': 330, 'cf': 400, 'rf': 330},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'large', 'opened': 1968
    },
    'Dodger Stadium': {
        'team': 'LAD', 'elevation': 340, 'hr_factor': 0.85,
        'dimensions': {'lf': 330, 'cf': 395, 'rf': 330},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 1962
    },
    'Petco Park': {
        'team': 'SD', 'elevation': 62, 'hr_factor': 0.85,
        'dimensions': {'lf': 336, 'cf': 396, 'rf': 322},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2004
    },
    'Comerica Park': {
        'team': 'DET', 'elevation': 585, 'hr_factor': 0.85,
        'dimensions': {'lf': 345, 'cf': 420, 'rf': 330},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 2000
    },
    'Oracle Park': {
        'team': 'SF', 'elevation': 12, 'hr_factor': 0.75,
        'dimensions': {'lf': 339, 'cf': 399, 'rf': 309},
        'wall_height': {'lf': 25, 'cf': 25, 'rf': 25},
        'foul_territory': 'large', 'opened': 2000
    },
    'Chase Field': {
        'team': 'ARI', 'elevation': 1100, 'hr_factor': 1.05,
        'dimensions': {'lf': 330, 'cf': 407, 'rf': 334},
        'wall_height': {'lf': 8, 'cf': 8, 'rf': 8},
        'foul_territory': 'average', 'opened': 1998
    }
}

# Stadium classification helpers
HITTER_FRIENDLY_PARKS = [
    'Coors Field', 'Yankee Stadium', 'Citizens Bank Park', 'Wrigley Field',
    'Fenway Park', 'Great American Ball Park', 'Oriole Park at Camden Yards',
    'Rogers Centre', 'Guaranteed Rate Field', 'Chase Field'
]

PITCHER_FRIENDLY_PARKS = [
    'Oracle Park', 'Comerica Park', 'Petco Park', 'Dodger Stadium',
    'Oakland Coliseum', 'Tropicana Field', 'Kauffman Stadium', 'Citi Field',
    'T-Mobile Park', 'Minute Maid Park', 'Busch Stadium'
]

NEUTRAL_PARKS = [
    'Truist Park', 'Globe Life Field', 'Target Field', 'Nationals Park'
]

# Elevation categories
HIGH_ELEVATION_PARKS = ['Coors Field',
                        'Chase Field', 'Truist Park']  # >1000 ft
MODERATE_ELEVATION_PARKS = [
    'Target Field', 'Kauffman Stadium', 'Progressive Field']  # 500-1000 ft
SEA_LEVEL_PARKS = ['Oracle Park', 'loanDepot park',
                   'T-Mobile Park', 'Tropicana Field']  # <50 ft

# Foul territory categories
LARGE_FOUL_TERRITORY = ['Oakland Coliseum', 'Tropicana Field',
                        'Kauffman Stadium', 'Angel Stadium', 'Oracle Park']
SMALL_FOUL_TERRITORY = ['Fenway Park', 'Yankee Stadium', 'Citizens Bank Park',
                        'Wrigley Field', 'Oriole Park at Camden Yards', 'Minute Maid Park']

# Feature columns for modeling
PLAYER_FEATURES = [
    'career_hr_rate', 'recent_hr_rate', 'slugging_pct', 'iso_power',
    'vs_lhp_hr_rate', 'vs_rhp_hr_rate', 'home_hr_rate', 'away_hr_rate',
    'clutch_hr_rate', 'recent_form_score', 'days_since_last_hr'
]

PITCHER_FEATURES = [
    'pitcher_hr_per_9', 'pitcher_fb_rate', 'pitcher_velocity',
    'pitcher_recent_era', 'handedness_matchup_factor'
]

ENVIRONMENTAL_FEATURES = [
    'stadium_hr_factor', 'temperature', 'wind_speed', 'wind_direction_factor',
    'humidity', 'elevation'
]

SITUATIONAL_FEATURES = [
    'inning', 'score_diff', 'runners_on_base', 'outs', 'lineup_position',
    'rest_days', 'is_day_game'
]

# Helper functions


def get_stadium_by_team(team_abbr: str) -> dict:
    """Get stadium information by team abbreviation"""
    for stadium_name, stadium_info in STADIUM_INFO.items():
        if stadium_info.get('team') == team_abbr:
            return {
                'name': stadium_name,
                **stadium_info
            }
    return None


def get_stadium_hr_factor(stadium_name: str) -> float:
    """Get home run factor for a stadium"""
    return STADIUM_INFO.get(stadium_name, {}).get('hr_factor', 1.0)


def is_hitter_friendly_park(stadium_name: str) -> bool:
    """Check if stadium is hitter-friendly"""
    return stadium_name in HITTER_FRIENDLY_PARKS


def is_pitcher_friendly_park(stadium_name: str) -> bool:
    """Check if stadium is pitcher-friendly"""
    return stadium_name in PITCHER_FRIENDLY_PARKS


def get_stadium_elevation(stadium_name: str) -> int:
    """Get stadium elevation in feet"""
    return STADIUM_INFO.get(stadium_name, {}).get('elevation', 500)


def get_stadium_dimensions(stadium_name: str) -> dict:
    """Get stadium field dimensions"""
    return STADIUM_INFO.get(stadium_name, {}).get('dimensions', {'lf': 330, 'cf': 400, 'rf': 330})
