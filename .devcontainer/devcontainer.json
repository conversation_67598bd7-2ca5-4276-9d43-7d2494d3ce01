{"name": "Reddit MLB Sentiment Analysis", "image": "mcr.microsoft.com/devcontainers/python:3.11", "postCreateCommand": "pip install -r requirements.txt", "features": {"ghcr.io/devcontainers/features/git:1": {}}, "customizations": {"vscode": {"extensions": ["ms-python.python", "ms-python.black-formatter", "Augment.vscode-augment", "charliermarsh.ruff"], "settings": {"python.defaultInterpreterPath": "/usr/local/bin/python", "python.terminal.activateEnvironment": true, "python.analysis.extraPaths": ["./src"], "python.analysis.autoSearchPaths": true, "[python]": {"editor.formatOnSave": true, "editor.defaultFormatter": "charliermarsh.ruff"}}}}, "remoteUser": "vscode", "containerEnv": {"PYTHONPATH": "/workspaces/${localWorkspaceFolderBasename}:${PYTHONPATH}"}}