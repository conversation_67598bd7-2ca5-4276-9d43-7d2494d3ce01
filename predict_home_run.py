#!/usr/bin/env python3
"""
MLB Home Run Prediction Interface

A user-friendly script to predict home run probability for specific game scenarios.
"""

import pandas as pd
from src.mlb_hr_predictor.models.home_run_predictor import HomeRunPredictor
import sys
import os
from datetime import datetime, timedelta
import pybaseball as pyb

sys.path.append("src")


def main():
    """Main prediction interface"""
    pyb.cache.enable()
    print("=" * 60)
    print("MLB HOME RUN PREDICTOR")
    print("=" * 60)
    print()

    # Initialize predictor
    predictor = HomeRunPredictor()

    # Check if trained model exists
    model_path = "models/home_run_predictor.joblib"

    if os.path.exists(model_path):
        print("Loading trained model...")
        predictor.load_model(model_path)
        print("✓ Model loaded successfully!")
    else:
        print("No trained model found. Training a new model...")

        # Get current date and yesterday's date
        today = datetime.now()
        yesterday = today - timedelta(days=1)

        print(
            f"Training with data from 2024 and 2025 up to {yesterday.strftime('%Y-%m-%d')}"
        )

        # Collect training data for 2024 and 2025 up to yesterday
        current_year = today.year
        seasons_to_use = [2024]
        if current_year >= 2025:
            seasons_to_use.append(2025)

        training_data = predictor.collect_training_data(seasons=seasons_to_use)

        if training_data.empty:
            print("❌ Could not collect training data. Please check your data sources.")
            print("❌ Cannot proceed without real training data.")
            return

        # Train model
        predictor.train_model(training_data, model_type="xgboost")

        # Save model
        predictor.save_model(model_path)

    print()
    print("=" * 60)
    print("MAKE A PREDICTION")
    print("=" * 60)

    # Interactive prediction interface
    while True:
        try:
            print("\nEnter the following information for home run prediction:")
            print("-" * 50)

            # Get user input
            batter_name = input("Batter name (e.g., 'Aaron Judge'): ").strip()
            if not batter_name:
                batter_name = "Aaron Judge"

            pitcher_name = input("Pitcher name (e.g., 'Gerrit Cole'): ").strip()
            if not pitcher_name:
                pitcher_name = "Gerrit Cole"

            ballpark = input("Ballpark (e.g., 'Yankee Stadium'): ").strip()
            if not ballpark:
                ballpark = "Yankee Stadium"

            # Get game context
            print("\nGame Context:")
            inning = get_int_input("Inning (1-9+): ", default=5)
            outs = get_int_input("Outs (0-2): ", default=1)
            score_diff = get_int_input(
                "Score difference (+ if batter's team ahead): ", default=0
            )

            # Get count
            print("\nPitch Count:")
            balls = get_int_input("Balls (0-3): ", default=1)
            strikes = get_int_input("Strikes (0-2): ", default=1)

            # Get weather
            print("\nWeather Conditions:")
            temperature = get_int_input("Temperature (°F): ", default=75)
            wind_speed = get_int_input("Wind speed (mph): ", default=5)

            # Prepare game context
            game_context = {
                "game_state": {
                    "inning": inning,
                    "outs": outs,
                    "score_diff": score_diff,
                },
                "count": {"balls": balls, "strikes": strikes},
                "baserunners": {"first": False, "second": False, "third": False},
                "lineup_info": {
                    "position": 4,  # Cleanup hitter
                    "times_through_order": 1,
                },
                "player_info": {"rest_days": 1, "games_last_7": 5},
                "datetime": "2024-07-15 19:00:00",
                "is_day_game": False,
            }

            # Weather data
            weather_data = {
                "temperature": temperature,
                "wind_speed": wind_speed,
                "humidity": 60,
                "wind_direction": "out_to_rf" if wind_speed > 10 else "calm",
                "pressure": 30.0,
            }

            print("\n" + "=" * 50)
            print("PREDICTION RESULTS")
            print("=" * 50)

            # Make prediction
            probability = predictor.predict_home_run_probability(
                batter_name, pitcher_name, game_context, ballpark, weather_data
            )

            # Display results
            print(f"\nBatter: {batter_name}")
            print(f"Pitcher: {pitcher_name}")
            print(f"Ballpark: {ballpark}")
            print(f"Situation: Inning {inning}, {outs} outs, {balls}-{strikes} count")
            print(f"Weather: {temperature}°F, {wind_speed} mph wind")
            print()
            print(f"🏟️  HOME RUN PROBABILITY: {probability:.1%}")
            print()

            # Interpretation
            if probability > 0.15:
                print("🔥 HIGH probability - Great home run situation!")
            elif probability > 0.08:
                print("⚡ MODERATE probability - Good chance for a home run")
            elif probability > 0.04:
                print("📊 AVERAGE probability - Typical MLB at-bat")
            else:
                print("📉 LOW probability - Home run unlikely")

            print()
            print("-" * 50)

            # Ask for another prediction
            another = input("\nMake another prediction? (y/n): ").strip().lower()
            if another not in ["y", "yes"]:
                break

        except KeyboardInterrupt:
            print("\n\nGoodbye!")
            break
        except Exception as e:
            print(f"\n❌ Error making prediction: {str(e)}")
            print("Please try again with different inputs.")


def get_int_input(prompt: str, default: int) -> int:
    """Get integer input with default value"""
    try:
        value = input(f"{prompt}[{default}]: ").strip()
        return int(value) if value else default
    except ValueError:
        return default


def predict_todays_games():
    """Predict outcomes for today's MLB games"""

    print("=" * 60)
    print("TODAY'S MLB GAMES PREDICTIONS")
    print("=" * 60)

    # Initialize predictor
    predictor = HomeRunPredictor()

    # Load or train model
    model_path = "models/home_run_predictor.joblib"

    if os.path.exists(model_path):
        print("Loading trained model...")
        predictor.load_model(model_path)
        print("✓ Model loaded successfully!")
    else:
        print("Training model with recent data...")

        # Get current date and yesterday's date
        today = datetime.now()
        yesterday = today - timedelta(days=1)

        # Collect training data for 2024 and 2025 up to yesterday
        current_year = today.year
        seasons_to_use = [2024]
        if current_year >= 2025:
            seasons_to_use.append(2025)

        training_data = predictor.collect_training_data(
            seasons=seasons_to_use, end_date=yesterday.strftime("%Y-%m-%d")
        )

        if training_data.empty:
            print("❌ Could not collect training data from real sources.")
            print("❌ Cannot train model without real data. Please check data sources.")
            return

        # Train model
        predictor.train_model(training_data, model_type="xgboost")
        predictor.save_model(model_path)

    # Get today's date
    today_str = datetime.now().strftime("%Y-%m-%d")
    print(f"\nGetting games for {today_str}...")

    # Try to get today's games (this would require real MLB schedule data)
    try:
        todays_games = get_todays_mlb_games()

        if not todays_games:
            print("No games found for today or unable to fetch schedule.")
            return

        print(f"Found {len(todays_games)} games today:")
        print("-" * 50)

        for game in todays_games:
            predict_game_home_runs(predictor, game)

    except Exception as e:
        print(f"Error fetching today's games: {str(e)}")


def get_todays_mlb_games():
    """Get today's MLB games from schedule"""

    try:
        today = datetime.now()
        today_str = today.strftime("%Y-%m-%d")

        print(f"Fetching MLB schedule for {today_str}...")

        # Try to get real schedule data using pybaseball
        try:
            # Get schedule for multiple teams to find today's games
            teams = [
                "LAA",
                "HOU",
                "OAK",
                "TOR",
                "ATL",
                "MIL",
                "STL",
                "CHC",
                "ARI",
                "LAD",
                "SF",
                "CLE",
                "SEA",
                "MIA",
                "NYM",
                "WSH",
                "BAL",
                "SD",
                "PHI",
                "PIT",
                "TEX",
                "TB",
                "BOS",
                "CIN",
                "COL",
                "KC",
                "DET",
                "MIN",
                "CWS",
                "NYY",
            ]
            todays_games = []

            for team in teams:
                try:
                    schedule = pyb.schedule_and_record(
                        season=int(today.year), team=team
                    )
                    if not schedule.empty:
                        schedule["Date"] = pd.to_datetime(
                            schedule["Date"].str.replace(r"\s*\(.*?\)", "", regex=True)
                            + f" {today.year}"
                        )
                        # Filter for today's games
                        todays_schedule = schedule[
                            schedule["Date"].dt.strftime("%Y-%m-%d") == today_str
                        ]

                        for _, game in todays_schedule.iterrows():
                            # Determine home and away teams correctly based on H_A field
                            if game.get("Home_Away") == "Home":
                                # This team is playing at home
                                home_team = team
                                away_team = game.get("Opp", "Unknown")
                            else:
                                # This team is playing away
                                home_team = game.get("Opp", "Unknown")
                                away_team = team

                            game_info = {
                                "home_team": home_team,
                                "away_team": away_team,
                                "ballpark": get_ballpark_name(home_team),
                                "time": game.get("Time", "TBD"),
                                "probable_pitchers": get_probable_pitchers(
                                    home_team, away_team
                                ),
                            }

                            # Avoid duplicates - check both directions (LAA@HOU and HOU@LAA are the same game)
                            is_duplicate = any(
                                (
                                    g["home_team"] == game_info["home_team"]
                                    and g["away_team"] == game_info["away_team"]
                                )
                                or (
                                    g["home_team"] == game_info["away_team"]
                                    and g["away_team"] == game_info["home_team"]
                                )
                                for g in todays_games
                            )

                            if not is_duplicate:
                                todays_games.append(game_info)

                except Exception as e:
                    print(f"Error getting schedule for {team}: {str(e)}")
                    continue

            if todays_games:
                return todays_games

        except Exception as e:
            print(f"Error fetching real schedule: {str(e)}")

    except Exception as e:
        print(f"Error getting today's games: {str(e)}")
        return []


def get_key_players_for_game(game_info):
    """Get key players (top hitters) for a specific game"""

    home_team = game_info["home_team"]
    away_team = game_info["away_team"]

    # Get key players for each team
    home_players = get_team_key_players(home_team)
    away_players = get_team_key_players(away_team)

    key_matchups = []

    # Add home team's best hitters vs away pitcher
    away_pitcher = game_info["probable_pitchers"]["away"]
    for player in home_players:
        key_matchups.append({"batter": player, "team": "home", "pitcher": away_pitcher})

    # Add away team's best hitters vs home pitcher
    home_pitcher = game_info["probable_pitchers"]["home"]
    for player in away_players:
        key_matchups.append({"batter": player, "team": "away", "pitcher": home_pitcher})

    return key_matchups


def get_probable_pitchers(home_team, away_team):
    """Get actual probable starting pitchers using MLB Stats API"""

    # Try the new MLB pitcher fetcher first
    try:
        from src.mlb_hr_predictor.data_collection.mlb_pitcher_fetcher import (
            get_real_probable_pitchers,
        )
        from datetime import datetime

        today = datetime.now().strftime("%Y-%m-%d")
        pitchers = get_real_probable_pitchers(away_team, home_team, today)

        if pitchers["home"] != "TBD" and pitchers["away"] != "TBD":
            print(
                f"Found MLB API probable pitchers: {pitchers['away']} vs {pitchers['home']}"
            )
            return pitchers
        else:
            print(f"MLB API returned TBD for {away_team} @ {home_team}")

    except Exception as e:
        print(f"Error using MLB pitcher fetcher: {str(e)}")

    return {"home": "TBD", "away": "TBD"}


def get_team_key_players(team_abbr):
    """Get actual starting lineup players for a team using pybaseball"""

    try:
        import pybaseball as pyb
        from datetime import datetime

        # Get current season batting stats to find active players
        current_year = datetime.now().year

        # Get team's current batting stats (minimum 50 PA to filter active players)
        team_stats = pyb.batting_stats(current_year, qual=50)

        if not team_stats.empty and "Team" in team_stats.columns:
            # Filter by team
            team_players = team_stats[team_stats["Team"] == team_abbr]

            if not team_players.empty:
                # Sort by HR + RBI to get best hitters
                if "HR" in team_players.columns and "RBI" in team_players.columns:
                    team_players["power_score"] = team_players["HR"] + (
                        team_players["RBI"] * 0.5
                    )
                    top_players = team_players.nlargest(4, "power_score")[
                        "Name"
                    ].tolist()
                    return top_players[:4]  # Return top 4 actual players
                else:
                    # Fallback to first few players if stats not available
                    return team_players["Name"].head(4).tolist()

        print(f"Could not get real lineup for {team_abbr}, using fallback")

    except Exception as e:
        print(f"Error getting real lineup for {team_abbr}: {str(e)}")


def get_ballpark_name(team_abbr):
    """Get ballpark name from team abbreviation"""
    ballparks = {
        "NYY": "Yankee Stadium",
        "BOS": "Fenway Park",
        "LAD": "Dodger Stadium",
        "SF": "Oracle Park",
        "HOU": "Minute Maid Park",
        "ATL": "Truist Park",
        "PHI": "Citizens Bank Park",
        "SD": "Petco Park",
        "COL": "Coors Field",
        "CHC": "Wrigley Field",
        "CIN": "Great American Ball Park",
        "MIL": "American Family Field",
        "STL": "Busch Stadium",
        "PIT": "PNC Park",
        "WSH": "Nationals Park",
        "NYM": "Citi Field",
        "MIA": "loanDepot park",
        "TB": "Tropicana Field",
        "BAL": "Oriole Park at Camden Yards",
        "TOR": "Rogers Centre",
        "CLE": "Progressive Field",
        "DET": "Comerica Park",
        "KC": "Kauffman Stadium",
        "MIN": "Target Field",
        "CWS": "Guaranteed Rate Field",
        "TEX": "Globe Life Field",
        "LAA": "Angel Stadium",
        "OAK": "Oakland Coliseum",
        "SEA": "T-Mobile Park",
        "ARI": "Chase Field",
    }

    return ballparks.get(team_abbr, f"{team_abbr} Stadium")


def predict_game_home_runs(predictor, game_info):
    """Predict home runs for a specific game"""

    print(
        f"\n🏟️  {game_info['away_team']} @ {game_info['home_team']} - {game_info['time']}"
    )
    print(f"   Ballpark: {game_info['ballpark']}")
    print(
        f"   Pitchers: {game_info['probable_pitchers']['away']} vs {game_info['probable_pitchers']['home']}"
    )

    # Get real key players for this matchup
    key_matchups = get_key_players_for_game(game_info)

    # Default game context
    game_context = {
        "game_state": {"inning": 5, "outs": 1, "score_diff": 0},
        "count": {"balls": 1, "strikes": 1},
        "baserunners": {"first": False, "second": False, "third": False},
        "lineup_info": {"position": 4, "times_through_order": 1},
        "player_info": {"rest_days": 1, "games_last_7": 5},
        "datetime": f"{datetime.now().strftime('%Y-%m-%d')} 19:00:00",
        "is_day_game": False,
    }

    # Default weather
    weather_data = {
        "temperature": 75,
        "wind_speed": 8,
        "humidity": 60,
        "wind_direction": "out_to_rf",
        "pressure": 30.0,
    }

    total_expected_hrs = 0
    predictions_made = 0

    for matchup in key_matchups:
        if matchup["batter"] and matchup["pitcher"]:
            try:
                probability = predictor.predict_home_run_probability(
                    matchup["batter"],
                    matchup["pitcher"],
                    game_context,
                    game_info["ballpark"],
                    weather_data,
                )

                # Convert to Yes/No prediction (threshold at 5%)
                prediction = "YES" if probability >= 0.05 else "NO"
                confidence = (
                    "HIGH" if probability >= 0.08 or probability <= 0.02 else "MEDIUM"
                )

                print(
                    f"   {matchup['batter']} vs {matchup['pitcher']}: {prediction} ({probability:.1%}) - {confidence} confidence"
                )
                total_expected_hrs += probability
                predictions_made += 1

            except Exception as e:
                print(f"   Error predicting {matchup['batter']}: {str(e)}")

    if predictions_made > 0:
        avg_probability = total_expected_hrs / predictions_made
        estimated_total_hrs = total_expected_hrs * 8  # Estimate for full game

        print(f"   📊 Estimated total home runs in game: {estimated_total_hrs:.1f}")
        print(f"   📈 Average HR probability for key hitters: {avg_probability:.1%}")

    print("-" * 50)


def show_sample_game_predictions(predictor):
    """Show sample predictions when real games aren't available"""

    sample_games = [
        {
            "home_team": "NYY",
            "away_team": "BOS",
            "ballpark": "Yankee Stadium",
            "time": "19:05",
            "probable_pitchers": {"home": "Gerrit Cole", "away": "Chris Sale"},
        },
        {
            "home_team": "LAD",
            "away_team": "SF",
            "ballpark": "Dodger Stadium",
            "time": "22:10",
            "probable_pitchers": {"home": "Walker Buehler", "away": "Logan Webb"},
        },
    ]

    print("Sample game predictions:")
    for game in sample_games:
        predict_game_home_runs(predictor, game)


def demo_predictions():
    """Run some demo predictions"""

    print("=" * 60)
    print("DEMO PREDICTIONS")
    print("=" * 60)

    predictor = HomeRunPredictor()

    # Try to load existing model or collect real training data
    print("Loading or training model with real data...")
    try:
        predictor.load_model("models/home_run_predictor.joblib")
        print("✓ Loaded existing model")
    except:
        print("No existing model found. Collecting real training data...")
        from datetime import datetime, timedelta

        yesterday = datetime.now() - timedelta(days=1)
        seasons_to_use = [2024]

        training_data = predictor.collect_training_data(
            seasons=seasons_to_use, end_date=yesterday.strftime("%Y-%m-%d")
        )

        if training_data.empty:
            print("❌ Could not collect real training data for demo.")
            print("❌ Please ensure data sources are available.")
            return

        print("Training model with real data...")
        predictor.train_model(
            training_data, model_type="xgboost", tune_hyperparameters=False
        )

    # Demo scenarios
    scenarios = [
        {
            "name": "Power Hitter vs Weak Pitcher at Coors Field",
            "batter": "Aaron Judge",
            "pitcher": "Weak Pitcher",
            "ballpark": "Coors Field",
            "temp": 85,
            "wind": 15,
        },
        {
            "name": "Average Hitter vs Ace Pitcher at Petco Park",
            "batter": "Average Hitter",
            "pitcher": "Jacob deGrom",
            "ballpark": "Petco Park",
            "temp": 65,
            "wind": 3,
        },
        {
            "name": "Clutch Situation at Yankee Stadium",
            "batter": "Mookie Betts",
            "pitcher": "Closer Pitcher",
            "ballpark": "Yankee Stadium",
            "temp": 75,
            "wind": 8,
        },
    ]

    for scenario in scenarios:
        print(f"\n📊 {scenario['name']}")
        print("-" * 50)

        game_context = {
            "game_state": {"inning": 7, "outs": 1, "score_diff": -1},
            "count": {"balls": 2, "strikes": 1},
            "baserunners": {"first": False, "second": True, "third": False},
            "lineup_info": {"position": 4, "times_through_order": 2},
            "player_info": {"rest_days": 1, "games_last_7": 6},
        }

        weather_data = {
            "temperature": scenario["temp"],
            "wind_speed": scenario["wind"],
            "humidity": 55,
            "wind_direction": "out_to_rf",
        }

        probability = predictor.predict_home_run_probability(
            scenario["batter"],
            scenario["pitcher"],
            game_context,
            scenario["ballpark"],
            weather_data,
        )

        print(f"Home Run Probability: {probability:.1%}")


if __name__ == "__main__":
    if len(sys.argv) > 1:
        if sys.argv[1] == "demo":
            demo_predictions()
        elif sys.argv[1] == "today":
            predict_todays_games()
        elif sys.argv[1] == "games":
            predict_todays_games()
        else:
            print("Usage:")
            print("  python predict_home_run.py          # Interactive mode")
            print("  python predict_home_run.py demo     # Demo predictions")
            print("  python predict_home_run.py today    # Predict today's games")
            print("  python predict_home_run.py games    # Predict today's games")
    else:
        main()
